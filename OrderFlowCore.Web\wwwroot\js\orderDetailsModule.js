/**
 * Order Details Module - Reusable JavaScript module for order details functionality
 * This module provides common functionality for displaying and managing order details
 * across different views in the application.
 */
const OrderDetailsModule = (function() {
    'use strict';

    // Private variables
    let currentOrderId = null;
    let config = {
        showLoading: null,
        hideLoading: null,
        showMessage: null,
        showOrderDetails: null,
        hideOrderDetails: null
    };

    // Private helper functions
    function getStatusBadge(status) {
        const statusMap = {
            'موافق': '<span class="badge bg-success">موافق</span>',
            'مرفوض': '<span class="badge bg-danger">مرفوض</span>',
            'قيد الانتظار': '<span class="badge bg-warning">قيد الانتظار</span>',
            'تم التحويل': '<span class="badge bg-info">تم التحويل</span>',
            'تم الإلغاء': '<span class="badge bg-secondary">تم الإلغاء</span>',
            'تم الإعادة': '<span class="badge bg-warning">تم الإعادة</span>'
        };
        return statusMap[status] || `<span class="badge bg-secondary">${status}</span>`;
    }

    function populateBasicInfo(data) {
        $('#orderNumber').text(data.id || '');
        $('#orderDate').text(data.orderDate || '');
        $('#orderStatus').text(data.orderStatus || '');
        $('#orderType').text(data.orderType || '');
    }

    function populateEmployeeInfo(data) {
        $('#employeeName').text(data.employeeName || '');
        $('#department').text(data.department || '');
        $('#jobTitle').text(data.jobTitle || '');
        $('#employmentType').text(data.employmentType || '');
        $('#qualification').text(data.qualification || '');
        $('#employeeNumber').text(data.employeeNumber || '');
        $('#civilRecord').text(data.civilRecord || '');
        $('#nationality').text(data.nationality || '');
        $('#mobileNumber').text(data.mobileNumber || '');
        
        // Handle details
        if (data.details) {
            $('#detailsText').text(data.details);
            $('#detailsRow').show();
        } else {
            $('#detailsRow').hide();
        }
    }

    function populateApprovalStatus(data) {
        $('#managerApproval').html(getStatusBadge(data.confirmedByDepartmentManager || 'قيد الانتظار'));
        $('#assistantManagerApproval').html(getStatusBadge(data.confirmedByAssistantManager || 'قيد الانتظار'));
        $('#coordinatorApproval').html(getStatusBadge(data.confirmedByCoordinator || 'قيد الانتظار'));

        $('#cancellationReason').text(data.reasonForCancellation);
        $('#coordinatorDetails').text(data.coordinatorDetails);

    }

    function populateSupervisorPermissions(data) {
        // Map supervisor permissions
        const supervisorMap = {
            'employeeServicesPermission': data.supervisorOfEmployeeServices,
            'hrPlanningPermission': data.supervisorOfHumanResourcesPlanning,
            'itPermission': data.supervisorOfInformationTechnology,
            'attendanceControlPermission': data.supervisorOfAttendance,
            'medicalRecordsPermission': data.supervisorOfMedicalRecords,
            'payrollPermission': data.supervisorOfPayrollAndBenefits,
            'legalCompliancePermission': data.supervisorOfLegalAndCompliance,
            'hrServicesPermission': data.supervisorOfHumanResourcesServices,
            'housingPermission': data.supervisorOfHousing,
            'filesSectionPermission': data.supervisorOfFiles,
            'outpatientPermission': data.supervisorOfOutpatientClinics,
            'socialInsurancePermission': data.supervisorOfSocialSecurity,
            'inventoryControlPermission': data.supervisorOfInventoryControl,
            'revenueDevelopmentPermission': data.supervisorOfRevenueDevelopment,
            'securityPermission': data.supervisorOfSecurity,
            'medicalConsultationPermission': data.supervisorOfMedicalConsultation,
        };

        // Populate each supervisor permission
        Object.keys(supervisorMap).forEach(key => {
            const value = supervisorMap[key];
            $(`#${key}`).html(getStatusBadge(value || 'قيد الانتظار'));
        });
    }

    function populateHRManager(data) {
        $('#hrManagerApproval').html(getStatusBadge(data.hrManagerApproval || 'قيد الانتظار'));
    }

    // Public API
    return {
        /**
         * Initialize the module with configuration
         * @param {Object} options - Configuration options
         */
        init: function(options) {
            config = { ...config, ...options };
        },

        /**
         * Get the current configuration
         * @returns {Object} Current configuration
         */
        getConfig: function() {
            return config;
        },

        /**
         * Load order details from server
         * @param {number} orderId - The order ID to load
         * @param {string} endpoint - The endpoint to call
         * @param {Object} additionalData - Additional data to send with the request
         */
        loadOrderDetails: function(orderId, endpoint, additionalData = {}) {
            currentOrderId = orderId;
            
            if (config.showLoading) {
                config.showLoading();
            }
            
            const requestData = { orderId: orderId, ...additionalData };
            
            $.ajax({
                url: endpoint,
                type: 'POST',
                data: requestData,
                success: function(response) {
                    if (config.hideLoading) {
                        config.hideLoading();
                    }
                    
                    if (response.success) {
                        this.populateOrderDetails(response.data);
                        if (config.showOrderDetails) {
                            config.showOrderDetails();
                        }
                    } else {
                        if (config.showMessage) {
                            config.showMessage(response.message || 'حدث خطأ أثناء تحميل تفاصيل الطلب', 'error');
                        }
                    }
                }.bind(this),
                error: function() {
                    if (config.hideLoading) {
                        config.hideLoading();
                    }
                    if (config.showMessage) {
                        config.showMessage('حدث خطأ أثناء تحميل تفاصيل الطلب', 'error');
                    }
                }
            });
        },

        /**
         * Populate order details in the DOM
         * @param {Object} data - Order details data
         */
        populateOrderDetails: function(data) {
            populateBasicInfo(data);
            populateEmployeeInfo(data);
            populateApprovalStatus(data);
            populateSupervisorPermissions(data);
            populateHRManager(data);
            
            // Check for ActionRequire status and show warning
            this.checkActionRequireStatus(data);
        },

        /**
         * Check if order status is ActionRequire and show warning
         * @param {Object} data - Order details data
         */
        checkActionRequireStatus: function(data) {
            const actionRequireContainer = document.getElementById('actionRequireMessageContainer');
            if (!actionRequireContainer) return;

            // Clear previous messages
            actionRequireContainer.innerHTML = '';

            // Check if order status is ActionRequire
            if (data.orderStatus == 'ActionRequire' || data.orderStatus == 'يتطلب إجراءات') {
                const warningHtml = `
                    <div class="alert alert-warning alert-dismissible fade show" role="alert">
                        <div class="d-flex align-items-center">
                            <div class="me-3">
                                <i class="fas fa-exclamation-triangle text-warning" style="font-size: 1.5rem;"></i>
                            </div>
                            <div>
                                <h6 class="alert-heading mb-1"> تنبيه: هذا الطلب يتطلب إجراءات</h6>
                                <p class="mb-1"><strong>التفاصيل:</strong> ${data.coordinatorDetails || 'لا توجد تفاصيل إضافية'}</p>
                                <p class="mb-0"><small>نرجو مراجعة الإجراءات المطلوبة</small></p>
                            </div>
                        </div>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                `;
                actionRequireContainer.innerHTML = warningHtml;
            }
        },

        /**
         * Hide order details
         */
        hideOrderDetails: function() {
            if (config.hideOrderDetails) {
                config.hideOrderDetails();
            }
            currentOrderId = null;
        },

        /**
         * Get current order ID
         * @returns {number|null} Current order ID
         */
        getCurrentOrderId: function() {
            return currentOrderId;
        },

        /**
         * Clear all order details fields
         */
        clearOrderDetails: function() {
            const fields = [
                'orderNumber', 'orderDate', 'orderStatus', 'orderType',
                'employeeName', 'department', 'jobTitle', 'employmentType',
                'qualification', 'employeeNumber', 'civilRecord', 'nationality',
                'mobileNumber', 'managerApproval', 'assistantManagerApproval', 'coordinatorApproval',
                'cancellationReason', 'coordinatorDetails', 'hrManagerApproval'
            ];

            fields.forEach(field => {
                $(`#${field}`).text('');
            });

            // Hide conditional rows
            $('#detailsRow, #cancellationRow, #cancellationDetailsRow').hide();

            // Clear supervisor permissions
            const supervisorFields = [
                'medicalConsultationPermission', 'hrPlanningPermission', 'itPermission',
                'attendanceControlPermission', 'medicalRecordsPermission', 'payrollPermission',
                'legalCompliancePermission', 'hrServicesPermission', 'housingPermission',
                'filesSectionPermission', 'outpatientPermission', 'socialInsurancePermission',
                'inventoryControlPermission', 'revenueDevelopmentPermission', 'securityPermission',
                'employeeServicesPermission'
            ];

            supervisorFields.forEach(field => {
                $(`#${field}`).html(getStatusBadge('قيد الانتظار'));
            });

            // Clear action require message
            const actionRequireContainer = document.getElementById('actionRequireMessageContainer');
            if (actionRequireContainer) {
                actionRequireContainer.innerHTML = '';
            }
        },

        /**
         * Show order details section
         */
        showOrderDetails: function() {
            $('#orderDetails').show();
        },

        /**
         * Hide order details section
         */
        hideOrderDetailsSection: function() {
            $('#orderDetails').hide();
        },

        /**
         * Show loading state
         */
        showLoading: function() {
            if (config.showLoading) {
                config.showLoading();
            } else {
                $('#loading').show();
                $('#orderDetails').hide();
            }
        },

        /**
         * Hide loading state
         */
        hideLoading: function() {
            if (config.hideLoading) {
                config.hideLoading();
            } else {
                $('#loading').hide();
            }
        },

        /**
         * Show message
         * @param {string} message - Message to display
         * @param {string} type - Message type ('success' or 'error')
         */
        showMessage: function(message, type) {
            if (config.showMessage) {
                config.showMessage(message, type);
            } else {
                const alertClass = type === 'error' ? 'alert-danger' : 'alert-success';
                const icon = type === 'error' ? '❌' : '✅';
                $('#messageContainer').html(`<div class="alert ${alertClass} fade-in">${icon} ${message}</div>`);
                
                // Auto-hide success messages after 5 seconds
                if (type === 'success') {
                    setTimeout(() => {
                        $('#messageContainer .alert').fadeOut();
                    }, 5000);
                }
            }
        },

        /**
         * Confirm order action
         * @param {number} orderId - Order ID
         * @param {string} endpoint - Confirmation endpoint
         * @param {string} successMessage - Success message
         * @param {string} errorMessage - Error message
         * @param {Function} onSuccess - Success callback
         */
        confirmOrder: function(orderId, endpoint, successMessage, errorMessage, onSuccess) {
            if (confirm('هل أنت متأكد من تأكيد هذا الطلب؟\n\nسيتم إرسال الطلب للمرحلة التالية.')) {
                this.showLoading();
                $.ajax({
                    url: endpoint,
                    type: 'POST',
                    data: { orderId: orderId },
                    success: function () {
                        this.showMessage(successMessage, 'success');
                        if (onSuccess) {
                            onSuccess();
                        } else {
                            setTimeout(() => location.reload(), 1500);
                        }
                    }.bind(this),
                    error: function () {
                        this.hideLoading();
                        this.showMessage(errorMessage, 'error');
                    }.bind(this)
                });
            }
        },

        /**
         * Reject order action
         * @param {number} orderId - Order ID
         * @param {string} reason - Rejection reason
         * @param {string} endpoint - Rejection endpoint
         * @param {string} successMessage - Success message
         * @param {string} errorMessage - Error message
         * @param {Function} onSuccess - Success callback
         */
        rejectOrder: function(orderId, reason, endpoint, successMessage, errorMessage, onSuccess) {
            if (confirm(`هل أنت متأكد من إلغاء هذا الطلب؟\n\nسبب الإلغاء: ${reason}`)) {
                this.showLoading();
                $.ajax({
                    url: endpoint,
                    type: 'POST',
                    data: { orderId: orderId, reason: reason },
                    success: function () {
                        this.showMessage(successMessage, 'success');
                        if (onSuccess) {
                            onSuccess();
                        } else {
                            setTimeout(() => location.reload(), 1500);
                        }
                    }.bind(this),
                    error: function () {
                        this.hideLoading();
                        this.showMessage(errorMessage, 'error');
                    }.bind(this)
                });
            }
        }
    };
})();

// Make it available globally
window.OrderDetailsModule = OrderDetailsModule; 