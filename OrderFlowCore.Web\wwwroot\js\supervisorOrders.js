// supervisorOrders.js
// Handles follow-up records AJAX and UI logic for SupervisorOrders

document.addEventListener("DOMContentLoaded", function () {
    // Initialize OrderDetailsModule for ProcessOrder page
    OrderDetailsModule.init({
        showLoading: function() {
            var loading = document.getElementById('loading');
            if (loading) loading.style.display = '';
            var details = document.getElementById('orderDetails');
            if (details) details.style.display = 'none';
        },
        hideLoading: function() {
            var loading = document.getElementById('loading');
            if (loading) loading.style.display = 'none';
        },
        showMessage: function(message, type) {
            var alertClass = type === 'error' ? 'alert-danger' : 'alert-success';
            var msg = document.getElementById('messageContainer');
            if (msg) msg.innerHTML = `<div class="alert ${alertClass}">${message}</div>`;
        },
        showOrderDetails: function() {
            var details = document.getElementById('orderDetails');
            if (details) {
                details.style.display = '';
                details.classList.add('fade-in');
            }
            var quickActions = document.getElementById('quickActions');
            if (quickActions) quickActions.style.display = '';
        },
        hideOrderDetails: function() {
            var details = document.getElementById('orderDetails');
            if (details) details.style.display = 'none';
            var quickActions = document.getElementById('quickActions');
            if (quickActions) quickActions.style.display = 'none';
        }
    });

    // Only attach order selection handler if #orderSelect exists (ProcessOrder view)
    var orderSelect = document.getElementById('orderSelect');
    if (orderSelect) {
        orderSelect.addEventListener('change', function() {
            var orderId = this.value;
            if (orderId && orderId !== '') {
                OrderDetailsModule.loadOrderDetails(orderId, '/SupervisorOrders/GetOrderDetails');
            } else {
                OrderDetailsModule.hideOrderDetails();
            }
        });
    }

    // ProcessOrder: validation and confirmation modals
    var processMsg = document.getElementById('processOrderMessageContainer');
    function showProcessMsg(message, type) {
        if (processMsg) {
            var alertClass = type === 'error' || type === 'danger' ? 'alert-danger' : 'alert-success';
            processMsg.innerHTML = `<div class="alert ${alertClass}">${message}</div>`;
        }
    }
    // Confirm Order
    var confirmOrderBtn = document.getElementById('confirmOrderBtn');
    var confirmOrderModalBtn = document.getElementById('confirmOrderModalBtn');
    var confirmOrderForm = document.getElementById('confirmOrderForm');
    if (confirmOrderBtn && confirmOrderModalBtn && confirmOrderForm) {
        confirmOrderBtn.addEventListener('click', function (e) {
            e.preventDefault();
            var orderId = confirmOrderForm.querySelector('input[name="orderId"]').value;
            if (!orderId) {
                showProcessMsg('يرجى اختيار رقم الطلب.', 'danger');
                return;
            }
            var modal = new bootstrap.Modal(document.getElementById('confirmOrderModal'));
            modal.show();
        });
        confirmOrderModalBtn.addEventListener('click', function () {
            confirmOrderForm.submit();
        });
    }
    // Needs Action
    var needsActionBtn = document.getElementById('needsActionBtn');
    var needsActionModalBtn = document.getElementById('needsActionModalBtn');
    var needsActionForm = document.getElementById('needsActionForm');
    var actionRequiredInput = document.getElementById('actionRequiredInput');
    if (needsActionBtn && needsActionModalBtn && needsActionForm && actionRequiredInput) {
        needsActionBtn.addEventListener('click', function (e) {
            e.preventDefault();
            var orderId = needsActionForm.querySelector('input[name="orderId"]').value;
            var val = actionRequiredInput.value.trim();
            if (!orderId) {
                showProcessMsg('يرجى اختيار رقم الطلب.', 'danger');
                return;
            }
            if (!val || val.length < 2 || val.length > 100) {
                showProcessMsg('يرجى إدخال الإجراءات المطلوبة بشكل صحيح (2-100 حرف).', 'danger');
                return;
            }
            document.getElementById('needsActionModalReason').textContent = val;
            var modal = new bootstrap.Modal(document.getElementById('needsActionModal'));
            modal.show();
        });
        needsActionModalBtn.addEventListener('click', function () {
            needsActionForm.submit();
        });
    }
    // Reject Order
    var rejectOrderBtn = document.getElementById('rejectOrderBtn');
    var rejectOrderModalBtn = document.getElementById('rejectOrderModalBtn');
    var rejectOrderForm = document.getElementById('rejectOrderForm');
    var rejectReasonInput = document.getElementById('rejectReasonInput');
    if (rejectOrderBtn && rejectOrderModalBtn && rejectOrderForm && rejectReasonInput) {
        rejectOrderBtn.addEventListener('click', function (e) {
            e.preventDefault();
            var orderId = rejectOrderForm.querySelector('input[name="orderId"]').value;
            var val = rejectReasonInput.value.trim();
            if (!orderId) {
                showProcessMsg('يرجى اختيار رقم الطلب.', 'danger');
                return;
            }
            if (!val || val.length < 2 || val.length > 100) {
                showProcessMsg('يرجى إدخال سبب الإعادة بشكل صحيح (2-100 حرف).', 'danger');
                return;
            }
            document.getElementById('rejectOrderModalReason').textContent = val;
            var modal = new bootstrap.Modal(document.getElementById('rejectOrderModal'));
            modal.show();
        });
        rejectOrderModalBtn.addEventListener('click', function () {
            rejectOrderForm.submit();
        });
    }
});

(function () {
    // CSRF token for AJAX
    var token = $("input[name='__RequestVerificationToken']").first().val();

    function showAlert(message, type) {
        var msg = $('#followUpMessageContainer');
        if (msg.length) {
            var alertClass = type === 'error' || type === 'danger' ? 'alert-danger' : 'alert-success';
            msg.html(`<div class="alert ${alertClass}">${message}</div>`);
        } else {
            OrderDetailsModule.showMessage(message, type);
        }
    }

    // Only run follow-up records logic if #addFollowUpForm exists (FollowUpRecords view)
    if ($('#addFollowUpForm').length) {
        // Add follow-up record with validation
        $('#addFollowUpForm').on('submit', function (e) {
            e.preventDefault();
            var civil = $('#addCivilRegistry').val().trim();
            var owner = $('#addOwnerName').val().trim();
            var proc = $('#addSpecialProcedure').val().trim();
            if (!civil || civil.length < 2 || civil.length > 20) {
                showAlert('يرجى إدخال السجل المدني بشكل صحيح (2-20 حرف).', 'danger');
                return;
            }
            if (!owner || owner.length < 2 || owner.length > 50) {
                showAlert('يرجى إدخال اسم صاحب الطلب بشكل صحيح (2-50 حرف).', 'danger');
                return;
            }
            if (!proc || proc.length < 2 || proc.length > 100) {
                showAlert('يرجى إدخال الإجراء المطلوب بشكل صحيح (2-100 حرف).', 'danger');
                return;
            }
            var data = { CivilRecord: civil, OwnerName: owner, SpecialProcedure: proc };
            $.ajax({
                url: window.addFollowUpRecordUrl || '/SupervisorOrders/AddFollowUpRecordAjax',
                type: 'POST',
                contentType: 'application/json',
                headers: { 'RequestVerificationToken': token },
                data: JSON.stringify(data),
                success: function (res) {
                    if (res.success) {
                        $('#followUpTable tbody').append(`<tr data-civil="${data.CivilRecord}" data-owner="${data.OwnerName}" data-proc="${data.SpecialProcedure}">
                            <td>${data.CivilRecord}</td>
                            <td>${data.OwnerName}</td>
                            <td>${data.SpecialProcedure}</td>
                            <td><button type='button' class='btn btn-sm btn-primary edit-btn'>تعديل</button> <button type='button' class='btn btn-sm btn-danger delete-btn ms-1'>حذف</button></td>
                        </tr>`);
                        showAlert(res.message, 'success');
                        $('#addFollowUpForm')[0].reset();
                    } else {
                        showAlert(res.message, 'danger');
                    }
                },
                error: function () { showAlert('خطأ في الاتصال بالخادم', 'danger'); }
            });
        });

        // Edit (open modal)
        $(document).on('click', '.edit-btn', function () {
            var row = $(this).closest('tr');
            $('#editCivilRegistry').val(row.data('civil'));
            $('#editOwnerName').val(row.data('owner'));
            $('#editSpecialProcedure').val(row.data('proc'));
            var modal = new bootstrap.Modal(document.getElementById('editModal'));
            modal.show();
        });

        // Edit (submit) with validation
        $('#editFollowUpForm').on('submit', function (e) {
            e.preventDefault();
            var civil = $('#editCivilRegistry').val().trim();
            var owner = $('#editOwnerName').val().trim();
            var proc = $('#editSpecialProcedure').val().trim();
            if (!owner || owner.length < 2 || owner.length > 50) {
                showAlert('يرجى إدخال اسم صاحب الطلب بشكل صحيح (2-50 حرف).', 'danger');
                return;
            }
            if (!proc || proc.length < 2 || proc.length > 100) {
                showAlert('يرجى إدخال الإجراء المطلوب بشكل صحيح (2-100 حرف).', 'danger');
                return;
            }
            var data = { CivilRecord: civil, OwnerName: owner, SpecialProcedure: proc };
            $.ajax({
                url: window.editFollowUpRecordUrl || '/SupervisorOrders/EditFollowUpRecordAjax',
                type: 'POST',
                contentType: 'application/json',
                headers: { 'RequestVerificationToken': token },
                data: JSON.stringify(data),
                success: function (res) {
                    if (res.success) {
                        var row = $(`#followUpTable tr[data-civil='${data.CivilRecord}']`);
                        row.find('td:eq(1)').text(data.OwnerName);
                        row.find('td:eq(2)').text(data.SpecialProcedure);
                        row.data('owner', data.OwnerName);
                        row.data('proc', data.SpecialProcedure);
                        showAlert(res.message, 'success');
                        bootstrap.Modal.getInstance(document.getElementById('editModal')).hide();
                    } else {
                        showAlert(res.message, 'danger');
                    }
                },
                error: function () { showAlert('خطأ في الاتصال بالخادم', 'danger'); }
            });
        });

        // Delete follow-up record with Bootstrap modal confirmation
        var deleteCivil = null;
        var deleteRow = null;
        $(document).on('click', '.delete-btn', function () {
            var row = $(this).closest('tr');
            deleteCivil = row.data('civil');
            deleteRow = row;
            // Show info in modal
            $('#deleteModalRecordInfo').html(
                `<strong>السجل المدني:</strong> ${row.data('civil')}<br>` +
                `<strong>اسم صاحب الطلب:</strong> ${row.data('owner')}<br>` +
                `<strong>الإجراء المطلوب:</strong> ${row.data('proc')}`
            );
            var modal = new bootstrap.Modal(document.getElementById('deleteModal'));
            modal.show();
        });
        $('#confirmDeleteBtn').on('click', function () {
            if (!deleteCivil || !deleteRow) return;
            $.ajax({
                url: window.deleteFollowUpRecordUrl || '/SupervisorOrders/DeleteFollowUpRecordAjax',
                type: 'POST',
                contentType: 'application/json',
                headers: { 'RequestVerificationToken': token },
                data: JSON.stringify({ CivilRecord: deleteCivil }),
                success: function (res) {
                    if (res.success) {
                        deleteRow.remove();
                        showAlert(res.message, 'success');
                    } else {
                        showAlert(res.message, 'danger');
                    }
                    bootstrap.Modal.getInstance(document.getElementById('deleteModal')).hide();
                    deleteCivil = null;
                    deleteRow = null;
                },
                error: function () {
                    showAlert('خطأ في الاتصال بالخادم', 'danger');
                    bootstrap.Modal.getInstance(document.getElementById('deleteModal')).hide();
                    deleteCivil = null;
                    deleteRow = null;
                }
            });
        });
    }
})(); 