{"Version": 1, "WorkspaceRootPath": "E:\\Projects\\abozyad\\OrderFlowCore\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|e:\\projects\\abozyad\\orderflowcore\\orderflowcore.web\\wwwroot\\js\\supervisororders.js||{14D17961-FE51-464D-9111-C4AF11D7D99A}", "RelativeMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|solutionrelative:orderflowcore.web\\wwwroot\\js\\supervisororders.js||{14D17961-FE51-464D-9111-C4AF11D7D99A}"}, {"AbsoluteMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|e:\\projects\\abozyad\\orderflowcore\\orderflowcore.web\\views\\supervisororders\\processorder.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|solutionrelative:orderflowcore.web\\views\\supervisororders\\processorder.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|e:\\projects\\abozyad\\orderflowcore\\orderflowcore.web\\wwwroot\\js\\hrcoordinator.js||{14D17961-FE51-464D-9111-C4AF11D7D99A}", "RelativeMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|solutionrelative:orderflowcore.web\\wwwroot\\js\\hrcoordinator.js||{14D17961-FE51-464D-9111-C4AF11D7D99A}"}, {"AbsoluteMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|e:\\projects\\abozyad\\orderflowcore\\orderflowcore.web\\views\\hrcoordinator\\processorder.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|solutionrelative:orderflowcore.web\\views\\hrcoordinator\\processorder.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|e:\\projects\\abozyad\\orderflowcore\\orderflowcore.web\\controllers\\supervisororderscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|solutionrelative:orderflowcore.web\\controllers\\supervisororderscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|e:\\projects\\abozyad\\orderflowcore\\orderflowcore.web\\controllers\\hrcoordinatorcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|solutionrelative:orderflowcore.web\\controllers\\hrcoordinatorcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|e:\\projects\\abozyad\\orderflowcore\\orderflowcore.web\\viewmodels\\hrcoordinatorviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|solutionrelative:orderflowcore.web\\viewmodels\\hrcoordinatorviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|e:\\projects\\abozyad\\orderflowcore\\orderflowcore.web\\viewmodels\\supervisorordersviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|solutionrelative:orderflowcore.web\\viewmodels\\supervisorordersviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|e:\\projects\\abozyad\\orderflowcore\\orderflowcore.web\\views\\hrcoordinator\\index.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|solutionrelative:orderflowcore.web\\views\\hrcoordinator\\index.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|e:\\projects\\abozyad\\orderflowcore\\orderflowcore.web\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|solutionrelative:orderflowcore.web\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|e:\\projects\\abozyad\\orderflowcore\\orderflowcore.web\\views\\supervisororders\\index.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|solutionrelative:orderflowcore.web\\views\\supervisororders\\index.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|e:\\projects\\abozyad\\orderflowcore\\orderflowcore.web\\controllers\\usercontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|solutionrelative:orderflowcore.web\\controllers\\usercontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|e:\\projects\\abozyad\\orderflowcore\\orderflowcore.web\\views\\supervisororders\\followuprecords.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|solutionrelative:orderflowcore.web\\views\\supervisororders\\followuprecords.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|e:\\projects\\abozyad\\orderflowcore\\orderflowcore.web\\views\\supervisororders\\dashboard.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|solutionrelative:orderflowcore.web\\views\\supervisororders\\dashboard.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 2, "Children": [{"$type": "Document", "DocumentIndex": 2, "Title": "hrCoordinator.js", "DocumentMoniker": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\js\\hrCoordinator.js", "RelativeDocumentMoniker": "OrderFlowCore.Web\\wwwroot\\js\\hrCoordinator.js", "ToolTip": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\js\\hrCoordinator.js", "RelativeToolTip": "OrderFlowCore.Web\\wwwroot\\js\\hrCoordinator.js", "ViewState": "AgIAAAAAAAAAAAAAAAAAAIEDAAADAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001646|", "WhenOpened": "2025-07-16T18:51:52.642Z", "IsPinned": true, "EditorCaption": ""}, {"$type": "Bookmark", "Name": "ST:0:0:{aa2115a1-9712-457b-9047-dbb71ca2cdd2}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "supervisorOrders.js", "DocumentMoniker": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\js\\supervisorOrders.js", "RelativeDocumentMoniker": "OrderFlowCore.Web\\wwwroot\\js\\supervisorOrders.js", "ToolTip": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\js\\supervisorOrders.js", "RelativeToolTip": "OrderFlowCore.Web\\wwwroot\\js\\supervisorOrders.js", "ViewState": "AgIAAD8AAAAAAAAAAAAgwGIAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001646|", "WhenOpened": "2025-07-16T19:34:31.423Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 9, "Title": "appsettings.json", "DocumentMoniker": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\appsettings.json", "RelativeDocumentMoniker": "OrderFlowCore.Web\\appsettings.json", "ToolTip": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\appsettings.json", "RelativeToolTip": "OrderFlowCore.Web\\appsettings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAABkAAAA/AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-07-16T19:09:21.449Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 8, "Title": "Index.cshtml", "DocumentMoniker": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\Views\\HRCoordinator\\Index.cshtml", "RelativeDocumentMoniker": "OrderFlowCore.Web\\Views\\HRCoordinator\\Index.cshtml", "ToolTip": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\Views\\HRCoordinator\\Index.cshtml", "RelativeToolTip": "OrderFlowCore.Web\\Views\\HRCoordinator\\Index.cshtml", "ViewState": "AgIAAAcAAAAAAAAAAAAAACEAAABdAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-07-16T18:50:38.708Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 10, "Title": "Index.cshtml", "DocumentMoniker": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\Views\\SupervisorOrders\\Index.cshtml", "RelativeDocumentMoniker": "OrderFlowCore.Web\\Views\\SupervisorOrders\\Index.cshtml", "ToolTip": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\Views\\SupervisorOrders\\Index.cshtml", "RelativeToolTip": "OrderFlowCore.Web\\Views\\SupervisorOrders\\Index.cshtml", "ViewState": "AgIAADUAAAAAAAAAAAAAACEAAABdAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-07-16T17:13:01.778Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "ProcessOrder.cshtml", "DocumentMoniker": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\Views\\HRCoordinator\\ProcessOrder.cshtml", "RelativeDocumentMoniker": "OrderFlowCore.Web\\Views\\HRCoordinator\\ProcessOrder.cshtml", "ToolTip": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\Views\\HRCoordinator\\ProcessOrder.cshtml", "RelativeToolTip": "OrderFlowCore.Web\\Views\\HRCoordinator\\ProcessOrder.cshtml", "ViewState": "AgIAAFkBAAAAAAAAAAAcwHoBAAAoAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-07-16T17:52:08.721Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "HRCoordinatorViewModel.cs", "DocumentMoniker": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\ViewModels\\HRCoordinatorViewModel.cs", "RelativeDocumentMoniker": "OrderFlowCore.Web\\ViewModels\\HRCoordinatorViewModel.cs", "ToolTip": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\ViewModels\\HRCoordinatorViewModel.cs", "RelativeToolTip": "OrderFlowCore.Web\\ViewModels\\HRCoordinatorViewModel.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-16T17:50:59.04Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "SupervisorOrdersViewModel.cs", "DocumentMoniker": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\ViewModels\\SupervisorOrdersViewModel.cs", "RelativeDocumentMoniker": "OrderFlowCore.Web\\ViewModels\\SupervisorOrdersViewModel.cs", "ToolTip": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\ViewModels\\SupervisorOrdersViewModel.cs", "RelativeToolTip": "OrderFlowCore.Web\\ViewModels\\SupervisorOrdersViewModel.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAqwAYAAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-16T17:49:15.802Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "SupervisorOrdersController.cs", "DocumentMoniker": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\Controllers\\SupervisorOrdersController.cs", "RelativeDocumentMoniker": "OrderFlowCore.Web\\Controllers\\SupervisorOrdersController.cs", "ToolTip": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\Controllers\\SupervisorOrdersController.cs", "RelativeToolTip": "OrderFlowCore.Web\\Controllers\\SupervisorOrdersController.cs", "ViewState": "AgIAADgAAAAAAAAAAAAhwD8AAAAlAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-16T17:43:26.16Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 11, "Title": "UserController.cs", "DocumentMoniker": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\Controllers\\UserController.cs", "RelativeDocumentMoniker": "OrderFlowCore.Web\\Controllers\\UserController.cs", "ToolTip": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\Controllers\\UserController.cs", "RelativeToolTip": "OrderFlowCore.Web\\Controllers\\UserController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-16T17:43:25.015Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "HRCoordinatorController.cs", "DocumentMoniker": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\Controllers\\HRCoordinatorController.cs", "RelativeDocumentMoniker": "OrderFlowCore.Web\\Controllers\\HRCoordinatorController.cs", "ToolTip": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\Controllers\\HRCoordinatorController.cs", "RelativeToolTip": "OrderFlowCore.Web\\Controllers\\HRCoordinatorController.cs", "ViewState": "AgIAACQAAAAAAAAAAAAgwEYAAABGAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-16T17:43:15.53Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "ProcessOrder.cshtml", "DocumentMoniker": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\Views\\SupervisorOrders\\ProcessOrder.cshtml", "RelativeDocumentMoniker": "OrderFlowCore.Web\\Views\\SupervisorOrders\\ProcessOrder.cshtml", "ToolTip": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\Views\\SupervisorOrders\\ProcessOrder.cshtml", "RelativeToolTip": "OrderFlowCore.Web\\Views\\SupervisorOrders\\ProcessOrder.cshtml", "ViewState": "AgIAAFQAAAAAAAAAAAAAAHAAAABWAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-07-16T17:13:09.74Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 13, "Title": "Dashboard.cshtml", "DocumentMoniker": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\Views\\SupervisorOrders\\Dashboard.cshtml", "RelativeDocumentMoniker": "OrderFlowCore.Web\\Views\\SupervisorOrders\\Dashboard.cshtml", "ToolTip": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\Views\\SupervisorOrders\\Dashboard.cshtml", "RelativeToolTip": "OrderFlowCore.Web\\Views\\SupervisorOrders\\Dashboard.cshtml", "ViewState": "AgIAANIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-07-16T17:12:43.449Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 12, "Title": "FollowUpRecords.cshtml", "DocumentMoniker": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\Views\\SupervisorOrders\\FollowUpRecords.cshtml", "RelativeDocumentMoniker": "OrderFlowCore.Web\\Views\\SupervisorOrders\\FollowUpRecords.cshtml", "ToolTip": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\Views\\SupervisorOrders\\FollowUpRecords.cshtml", "RelativeToolTip": "OrderFlowCore.Web\\Views\\SupervisorOrders\\FollowUpRecords.cshtml", "ViewState": "AgIAACkAAAAAAAAAAAAAAHIAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-07-16T17:12:09.499Z", "EditorCaption": ""}]}]}]}