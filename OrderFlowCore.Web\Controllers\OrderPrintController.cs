using OrderFlowCore.Web.ViewModels;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Application.DTOs;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.AspNetCore.Http.HttpResults;
using OrderFlowCore.Core.Entities;

namespace OrderFlowCore.Web.Controllers
{
    public class OrderPrintController : Controller
    {
        private readonly IOrderPrintService _orderPrintService;

        public OrderPrintController(IOrderPrintService orderPrintService)
        {
            _orderPrintService = orderPrintService;
        }

        // GET: /OrderPrint/
        public async Task<ActionResult> Index(string searchTerm = "", string filter = "today")
        {
            var ordersResult = await _orderPrintService.GetPrintableOrdersAsync(searchTerm, filter);
            var model = new OrderPrintViewModel
            {
                SearchTerm = searchTerm,
                Filter = filter,
                Orders = ordersResult.Data?.Select(o => new SelectListItem
                {
                    Value = o.OrderId.ToString(),
                    Text = $"{o.OrderNumber} | {o.EmployeeName}"
                }).ToList() ?? new List<SelectListItem>(),
                SelectedOrder = null
            };
            return View(model);
        }

        [HttpPost]
        public async Task<ActionResult> Index(OrderPrintViewModel model)
        {
            var ordersResult = await _orderPrintService.GetPrintableOrdersAsync(model.SearchTerm, model.Filter);
            model.Orders = ordersResult.Data?.Select(o => new SelectListItem
            {
                Value = o.OrderId.ToString(),
                Text = $"{o.OrderNumber} | {o.EmployeeName}"
            }).ToList() ?? new List<SelectListItem>();

            if (model.SelectedOrder != null && int.TryParse(model.SelectedOrder.OrderNumber, out int orderId))
            {
                var detailsResult = await _orderPrintService.GetOrderPrintDetailsAsync(orderId);
                if (detailsResult.IsSuccess && detailsResult.Data != null)
                {
                    model.SelectedOrder = new OrderPrintDetailsViewModel
                    {
                        OrderNumber = detailsResult.Data.OrderNumber,
                        OrderDate = detailsResult.Data.OrderDate,
                        OrderStatus = detailsResult.Data.OrderStatus,
                        OrderType = detailsResult.Data.OrderType,
                        EmployeeName = detailsResult.Data.EmployeeName,
                        Department = detailsResult.Data.Department,
                        Notes = detailsResult.Data.Notes,
                        JobTitle = detailsResult.Data.JobTitle,
                        EmployeeNumber = detailsResult.Data.EmployeeNumber,
                        CivilRecord = detailsResult.Data.CivilRecord,
                        Nationality = detailsResult.Data.Nationality,
                        MobileNumber = detailsResult.Data.MobileNumber,
                        EmploymentType = detailsResult.Data.EmploymentType,
                        Qualification = detailsResult.Data.Qualification,
                        ManagerApproval = detailsResult.Data.ManagerApproval,
                        SupervisorApproval = detailsResult.Data.SupervisorApproval,
                        CoordinatorApproval = detailsResult.Data.CoordinatorApproval,
                        CancellationReason = detailsResult.Data.CancellationReason,
                        CoordinatorDetails = detailsResult.Data.CoordinatorDetails,
                        HRManagerApproval = detailsResult.Data.HRManagerApproval
                    };
                }
            }
            return View(model);
        }

        [HttpGet]
        public async Task<PartialViewResult> GetOrderDetails(int orderId)
        {
            var detailsResult = await _orderPrintService.GetOrderPrintDetailsAsync(orderId);
            var vm = detailsResult.Data == null ? null : new OrderPrintDetailsViewModel
            {
                OrderNumber = detailsResult.Data.OrderNumber,
                OrderDate = detailsResult.Data.OrderDate,
                OrderStatus = detailsResult.Data.OrderStatus,
                OrderType = detailsResult.Data.OrderType,
                EmployeeName = detailsResult.Data.EmployeeName,
                Department = detailsResult.Data.Department,
                Notes = detailsResult.Data.Notes,
                JobTitle = detailsResult.Data.JobTitle,
                EmployeeNumber = detailsResult.Data.EmployeeNumber,
                CivilRecord = detailsResult.Data.CivilRecord,
                Nationality = detailsResult.Data.Nationality,
                MobileNumber = detailsResult.Data.MobileNumber,
                EmploymentType = detailsResult.Data.EmploymentType,
                Qualification = detailsResult.Data.Qualification,
                ManagerApproval = detailsResult.Data.ManagerApproval,
                SupervisorApproval = detailsResult.Data.SupervisorApproval,
                CoordinatorApproval = detailsResult.Data.CoordinatorApproval,
                CancellationReason = detailsResult.Data.CancellationReason,
                CoordinatorDetails = detailsResult.Data.CoordinatorDetails,
                HRManagerApproval = detailsResult.Data.HRManagerApproval
            };
            return PartialView("_OrderDetailsPanel", vm);
        }

        [HttpPost]
        public async Task<ActionResult> DownloadAttachments(int orderId)
        {
            var result = await _orderPrintService.DownloadOrderAttachmentsZipAsync(orderId);
            if (!result.IsSuccess || result.Data == null)
                return NotFound("No attachments found.");
            return File(result.Data, "application/zip", $"مرفقات_{orderId}.zip");
        }

        [HttpPost]
        public async Task<ActionResult> PrintOrder(int orderId)
        {
            var result = await _orderPrintService.GenerateOrderPdfAsync(orderId);
            if (!result.IsSuccess || result.Data == null)
                return NotFound("PDF generation failed.");
            return File(result.Data, "application/pdf", $"طلب_{orderId}.pdf");
        }
    }
} 