namespace OrderFlowCore.Application.DTOs
{
    public class SupervisorsFollowUpDto
    {
        public string SupervisorId { get; set; }
        public string CivilRecord { get; set; }
        public string OwnerName { get; set; }
        public string SpecialProcedure { get; set; }
        // Optionally, add an Id property for UI operations (not in entity)
        public int? Id { get; set; }
    }
} 