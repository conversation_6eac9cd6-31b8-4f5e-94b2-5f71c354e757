using Microsoft.Extensions.DependencyInjection;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Application.Services;

namespace OrderFlowCore.Application
{
    public static class DependencyInjection
    {
        public static IServiceCollection AddApplication(this IServiceCollection services)
        {
            // Register application services here (if any)
            services.AddScoped<IAuthService, AuthService>();
            services.AddScoped<IUserService, UserService>();
            services.AddScoped<IDepartmentService, DepartmentService>();
            services.AddScoped<IJobTypeService, JobTypeService>();
            services.AddScoped<IEmploymentTypeService, EmploymentTypeService>();
            services.AddScoped<IQualificationService, QualificationService>();
            services.AddScoped<IOrdersTypeService, OrdersTypeService>();
            services.AddScoped<INationalityService, NationalityService>();
            // Register specialized order services
            services.AddScoped<IOrderManagementService, OrderManagementService>();
            services.AddScoped<IDirectManagerOrderService, DirectManagerOrderService>();
            services.AddScoped<IAssistantManagerOrderService, AssistantManagerOrderService>();
            services.AddScoped<IHRCoordinatorOrderService, HRCoordinatorOrderService>();
            services.AddScoped<ISupervisorOrderService, SupervisorOrderService>();
            services.AddScoped<IOrderPrintService, OrderPrintService>();
            services.AddScoped<IOrderRestorationService, OrderRestorationService>();

            // Register new thin controller services
            services.AddScoped<IDistributionService, DistributionService>();
            services.AddScoped<IAccountManagementService, AccountManagementService>();
            services.AddScoped<IEmployeeService, EmployeeService>();
            services.AddScoped<IPathManagementService, PathManagementService>();
            services.AddScoped<ICachedDropdownService, CachedDropdownService>();
            services.AddSingleton<IPerformanceMonitoringService, PerformanceMonitoringService>();
            services.AddScoped<IDashboardService, DashboardService>();
            services.AddScoped<ISupervisorService, SupervisorService>();
            services.AddScoped<ISupervisorsFollowUpService, SupervisorsFollowUpService>();

            return services;
        }
    }
} 