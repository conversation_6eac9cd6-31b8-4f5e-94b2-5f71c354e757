using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using OrderFlowCore.Application.Common;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Helper;
using OrderFlowCore.Application.Interfaces;
using OrderFlowCore.Application.Interfaces.Repositories;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Core.Entities;
using OrderFlowCore.Core.Exceptions;
using OrderFlowCore.Core.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace OrderFlowCore.Application.Services
{
    public class OrderService : IOrderService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IEnvironmentService _env;
        private readonly OrderServiceOptions _options;
        private readonly IFileService _fileService;
        private readonly ILogger<OrderService> _logger;

        public OrderService(
            IUnitOfWork unitOfWork,
            IEnvironmentService env,
            IOptions<OrderServiceOptions> options,
            IFileService fileService,
            ILogger<OrderService> logger)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _env = env ?? throw new ArgumentNullException(nameof(env));
            _options = options?.Value ?? throw new ArgumentNullException(nameof(options));
            _fileService = fileService ?? throw new ArgumentNullException(nameof(fileService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public async Task<ServiceResult<DropdownDataDto>> GetDropdownDataAsync()
        {
            try
            {

                var dropdownData = new DropdownDataDto();

                // Load data from repositories with error handling
                var departments = await _unitOfWork.Departments.GetAllAsync();
                var orderTypes = await _unitOfWork.OrdersTypes.GetAllAsync();
                var jobTypes = await _unitOfWork.JobTypes.GetAllAsync();
                var nationalities = await _unitOfWork.Nationalities.GetAllAsync();
                var employmentTypes = await _unitOfWork.EmploymentTypes.GetAllAsync();
                var qualifications = await _unitOfWork.Qualifications.GetAllAsync();

                // Convert data to DropdownItemDto
                dropdownData.Departments = departments.Select(d => new DropdownItemDto { Value = d.Name, Text = d.Name }).ToList();
                dropdownData.OrderTypes = orderTypes.Select(ot => new DropdownItemDto { Value = ot.Name, Text = ot.Name }).ToList();
                dropdownData.JobTitles = jobTypes.Select(jt => new DropdownItemDto { Value = jt.Name, Text = jt.Name }).ToList();
                dropdownData.Nationalities = nationalities.Select(n => new DropdownItemDto { Value = n.Name, Text = n.Name }).ToList();
                dropdownData.EmploymentTypes = employmentTypes.Select(et => new DropdownItemDto { Value = et.Name, Text = et.Name }).ToList();
                dropdownData.Qualifications = qualifications.Select(q => new DropdownItemDto { Value = q.Name, Text = q.Name }).ToList();

                return ServiceResult<DropdownDataDto>.Success(dropdownData);
            }
            catch (Exception ex)
            {
                return ServiceResult<DropdownDataDto>.Failure($"خطأ في تحميل بيانات القوائم المنسدلة: {ex.Message}");
            }
        }

        public async Task<ServiceResult<OrderSummaryDto>> CreateOrderAsync(OrderNewDto orderDto)
        {
            try
            {

                // Validate input
                var validationResult = ValidateOrderDto(orderDto);
                if (!validationResult.IsSuccess)
                {
                    return ServiceResult<OrderSummaryDto>.Failure(validationResult.Message, validationResult.Errors);
                }

                var order = new OrdersTable
                {
                    EmployeeName = orderDto.EmployeeName.Trim(),
                    JobTitle = orderDto.JobTitle.Trim(),
                    EmployeeNumber = orderDto.EmployeeNumber.Trim(),
                    CivilRecord = orderDto.CivilRecord.Trim(),
                    Nationality = orderDto.Nationality.Trim(),
                    MobileNumber = orderDto.MobileNumber.Trim(),
                    Department = orderDto.Department.Trim(),
                    EmploymentType = orderDto.EmploymentType.Trim(),
                    Qualification = orderDto.Qualification.Trim(),
                    OrderType = orderDto.OrderType.Trim(),
                    Details = orderDto.Details?.Trim() ?? string.Empty,
                    CreatedAt = DateTime.UtcNow,
                    OrderStatus = OrderStatus.DM,
                };

                // Use FileService for attachments
                var attachmentResult = await _fileService.UploadFilesAsync(orderDto.Attachments, $"order_{orderDto.CivilRecord}");
                if (!attachmentResult.IsSuccess)
                {
                    return ServiceResult<OrderSummaryDto>.Failure(attachmentResult.Message);
                }

                for (int i = 0; i < attachmentResult.Data.Count && i < _options.MaxAttachments; i++)
                {
                    var url = attachmentResult.Data[i];
                    switch (i)
                    {
                        case 0: order.File1Url = url; break;
                        case 1: order.File2Url = url; break;
                        case 2: order.File3Url = url; break;
                        case 3: order.File4Url = url; break;
                    }
                }

                await _unitOfWork.Orders.AddAsync(order);
                await _unitOfWork.SaveChangesAsync();

                return ServiceResult<OrderSummaryDto>.Success(new OrderSummaryDto
                {
                    Id = order.Id,
                    EmployeeName = order.EmployeeName,
                    OrderType = order.OrderType,
                    Department = order.Department,
                    CreatedAt = order.CreatedAt,
                    OrderStatus = order.OrderStatus
                }, "تم إنشاء الطلب بنجاح");
            }
            catch (Exception ex)
            {
                return ServiceResult<OrderSummaryDto>.Failure($"خطأ في إنشاء الطلب: {ex.Message}");
            }
        }

        public async Task<ServiceResult<OrderDetailsDto>> GetOrderDetailsAsync(int orderId)
        {
            try
            {
                _logger.LogInformation("Getting order details for order ID: {OrderId}", orderId);

                if (orderId <= 0)
                {
                    _logger.LogWarning("Invalid order ID provided: {OrderId}", orderId);
                    throw new ValidationException("رقم الطلب غير صحيح");
                }

                var order = await _unitOfWork.Orders.GetByIdAsync(orderId);
                if (order == null)
                {
                    _logger.LogWarning("Order not found: {OrderId}", orderId);
                    throw new OrderNotFoundException(orderId);
                }

                var attachments = BuildAttachmentList(order);
                var orderDetails = OrderDetailsDto.FromDomain(order);

                _logger.LogInformation("Successfully retrieved order details for order ID: {OrderId}", orderId);
                return ServiceResult<OrderDetailsDto>.Success(orderDetails);
            }
            catch (BusinessLogicException)
            {
                throw; // Re-throw business logic exceptions
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting order details for order ID: {OrderId}", orderId);
                return ServiceResult<OrderDetailsDto>.Failure($"خطأ في جلب تفاصيل الطلب: {ex.Message}");
            }
        }

        public async Task<ServiceResult<OrderDetailsDto>> GetOrderDetailsByIdAndCivilRecordAsync(int orderId, string civilRecord)
        {
            try
            {
                if (orderId <= 0 || string.IsNullOrWhiteSpace(civilRecord))
                {
                    return ServiceResult<OrderDetailsDto>.Failure("رقم الطلب أو السجل المدني غير صحيح");
                }

                var order = await _unitOfWork.Orders.GetByIdAsync(orderId);
                if (order == null || !order.CivilRecord.Equals(civilRecord.Trim(), StringComparison.OrdinalIgnoreCase))
                {
                    return ServiceResult<OrderDetailsDto>.Failure("رقم الطلب أو السجل المدني غير صحيح");
                }

                var orderDetails = OrderDetailsDto.FromDomain(order);
                return ServiceResult<OrderDetailsDto>.Success(orderDetails);
            }
            catch (Exception ex)
            {
                return ServiceResult<OrderDetailsDto>.Failure($"خطأ في جلب تفاصيل الطلب: {ex.Message}");
            }
        }

        public async Task<ServiceResult> UploadAttachmentAsync(int orderId, byte[] fileData, string fileName, int fileNumber)
        {
            try
            {
                if (orderId <= 0)
                {
                    return ServiceResult.Failure("رقم الطلب غير صحيح");
                }

                if (fileData == null || fileData.Length == 0)
                {
                    return ServiceResult.Failure("بيانات الملف فارغة");
                }

                if (string.IsNullOrWhiteSpace(fileName))
                {
                    return ServiceResult.Failure("اسم الملف مطلوب");
                }

                var order = await _unitOfWork.Orders.GetByIdAsync(orderId);
                if (order == null)
                {
                    return ServiceResult.Failure("لم يتم العثور على الطلب");
                }

                // Determine which file slot to update and delete old file if exists
                string oldFileUrl = null;
                switch (fileNumber)
                {
                    case 1:
                        oldFileUrl = order.File1Url;
                        break;
                    case 2:
                        oldFileUrl = order.File2Url;
                        break;
                    case 3:
                        oldFileUrl = order.File3Url;
                        break;
                    case 4:
                        oldFileUrl = order.File4Url;
                        break;
                    default:
                        return ServiceResult.Failure("رقم المرفق غير صحيح");
                }

                if (!string.IsNullOrEmpty(oldFileUrl))
                {
                    await _fileService.DeleteFileAsync(oldFileUrl);
                }

                // Upload new file
                var uploadResult = await _fileService.UploadFileAsync(fileData, fileName, $"order_{order.CivilRecord}");
                if (!uploadResult.IsSuccess)
                {
                    return ServiceResult.Failure(uploadResult.Message);
                }
                var fileUrl = uploadResult.Data;

                // Update the correct file URL property
                switch (fileNumber)
                {
                    case 1:
                        order.File1Url = fileUrl;
                        break;
                    case 2:
                        order.File2Url = fileUrl;
                        break;
                    case 3:
                        order.File3Url = fileUrl;
                        break;
                    case 4:
                        order.File4Url = fileUrl;
                        break;
                }

                await _unitOfWork.Orders.UpdateAsync(order);
                await _unitOfWork.SaveChangesAsync();

                return ServiceResult.Success("تم رفع المرفق بنجاح");
            }
            catch (Exception ex)
            {
                return ServiceResult.Failure($"خطأ في رفع المرفق: {ex.Message}");
            }
        }

        public async Task<ServiceResult<byte[]>> DownloadAttachmentsZipAsync(int orderId)
        {
            try
            {

                if (orderId <= 0)
                {
                    return ServiceResult<byte[]>.Failure("رقم الطلب غير صحيح");
                }

                var order = await _unitOfWork.Orders.GetByIdAsync(orderId);
                if (order == null)
                {
                    return ServiceResult<byte[]>.Failure("لم يتم العثور على الطلب");
                }

                var fileUrls = new List<string>();
                if (!string.IsNullOrEmpty(order.File1Url)) fileUrls.Add(order.File1Url);
                if (!string.IsNullOrEmpty(order.File2Url)) fileUrls.Add(order.File2Url);
                if (!string.IsNullOrEmpty(order.File3Url)) fileUrls.Add(order.File3Url);
                if (!string.IsNullOrEmpty(order.File4Url)) fileUrls.Add(order.File4Url);

                if (fileUrls.Count == 0)
                {
                    return ServiceResult<byte[]>.Failure("لا توجد مرفقات");
                }

                // Use FileService for zipping
                var zipResult = await _fileService.DownloadFilesZipAsync(fileUrls);
                if (!zipResult.IsSuccess)
                {
                    return ServiceResult<byte[]>.Failure(zipResult.Message);
                }

                return ServiceResult<byte[]>.Success(zipResult.Data);
            }
            catch (Exception ex)
            {
                return ServiceResult<byte[]>.Failure($"خطأ في تحميل المرفقات: {ex.Message}");
            }
        }


        // DirectManager specific methods
        public async Task<ServiceResult<List<OrderSummaryDto>>> GetPendingOrdersForDirectMangerAsync()
        {
            try
            {
                // Get orders that are pending manager approval
                var pendingOrders = await _unitOfWork.Orders.GetPendingOrdersForDirectMangerAsync();

                var orderSummaries = pendingOrders.Select(order => new OrderSummaryDto
                {
                    Id = order.Id,
                    EmployeeName = order.EmployeeName,
                    OrderType = order.OrderType,
                    Department = order.Department,
                    CreatedAt = order.CreatedAt,
                    OrderStatus = order.OrderStatus
                }).ToList();

                return ServiceResult<List<OrderSummaryDto>>.Success(orderSummaries);
            }
            catch (Exception ex)
            {
                return ServiceResult<List<OrderSummaryDto>>.Failure($"خطأ في جلب الطلبات المعلقة: {ex.Message}");
            }
        }

        public async Task<ServiceResult> ConfirmOrderByDirectManagerAsync(int orderId, string? userName)
        {
            try
            {
                if (orderId <= 0)
                {
                    return ServiceResult.Failure("رقم الطلب غير صحيح");
                }

                var order = await _unitOfWork.Orders.GetByIdAsync(orderId);
                if (order == null)
                {
                    return ServiceResult.Failure("لم يتم العثور على الطلب");
                }


                var department = await _unitOfWork.Departments.GetByNameAsync(order.Department);
                var assistantManagerId = department?.AssistantManagerId ?? AssistantManagerType.Unknown;

                // تحديد المسار والحالة الجديدة
                OrderStatus newStatus = assistantManagerId.ToOrderStatus();
                string statusMessage = assistantManagerId == AssistantManagerType.B ?
                    "تم تأكيد الطلب بنجاح وتحويله إلى منسق الموارد البشرية" : "تم تأكيد الطلب بنجاح وتحويله إلى مساعد المدير";


                // Update order status
                order.OrderStatus = newStatus;
                order.ConfirmedByDepartmentManager = OrderHelper.ConfirmedBy(userName);

                await _unitOfWork.Orders.UpdateAsync(order);
                await _unitOfWork.SaveChangesAsync();

                return ServiceResult.Success(statusMessage);

            }
            catch (Exception ex)
            {
                return ServiceResult.Failure($"خطأ في تأكيد الطلب: {ex.Message}");
            }
        }

        public async Task<ServiceResult> RejectOrderByDirectManagerAsync(int orderId, string reason, string? userName)
        {
            try
            {
                if (orderId <= 0)
                {
                    return ServiceResult.Failure("رقم الطلب غير صحيح");
                }

                if (string.IsNullOrWhiteSpace(reason))
                {
                    return ServiceResult.Failure("سبب الإلغاء مطلوب");
                }

                var order = await _unitOfWork.Orders.GetByIdAsync(orderId);
                if (order == null)
                {
                    return ServiceResult.Failure("لم يتم العثور على الطلب");
                }


                // Update order status
                order.ConfirmedByDepartmentManager = OrderHelper.RejectedBy(userName);
                order.ReasonForCancellation = reason;
                order.OrderStatus = OrderStatus.CancelledByDepartmentManager;

                await _unitOfWork.Orders.UpdateAsync(order);
                await _unitOfWork.SaveChangesAsync();

                return ServiceResult.Success("تم إلغاء الطلب بنجاح");
            }
            catch (Exception ex)
            {
                return ServiceResult.Failure($"خطأ في إلغاء الطلب: {ex.Message}");
            }
        }


        // AssistantManager specific methods
        public async Task<ServiceResult<List<OrderSummaryDto>>> GetAssistantManagerOrdersAsync(AssistantManagerType assistantManagerId)
        {
            try
            {
                if (assistantManagerId == AssistantManagerType.Unknown)
                {
                    return ServiceResult<List<OrderSummaryDto>>.Failure("معرف مساعد المدير مطلوب");
                }

                // Get orders that are assigned to this assistant manager
                var assistantManagerOrders = await _unitOfWork.Orders.GetAssistantManagerOrdersAsync(assistantManagerId);

                var orderSummaries = assistantManagerOrders.Select(order => new OrderSummaryDto
                {
                    Id = order.Id,
                    EmployeeName = order.EmployeeName,
                    OrderType = order.OrderType,
                    Department = order.Department,
                    CreatedAt = order.CreatedAt,
                    OrderStatus = order.OrderStatus
                }).ToList();

                return ServiceResult<List<OrderSummaryDto>>.Success(orderSummaries);
            }
            catch (Exception ex)
            {
                return ServiceResult<List<OrderSummaryDto>>.Failure($"خطأ في جلب طلبات مساعد المدير: {ex.Message}");
            }
        }

        public async Task<ServiceResult> ConfirmOrderByAssistantManagerAsync(int orderId, string? userName)
        {
            try
            {
                if (orderId <= 0)
                {
                    return ServiceResult.Failure("رقم الطلب غير صحيح");
                }

                var order = await _unitOfWork.Orders.GetByIdAsync(orderId);
                if (order == null)
                {
                    return ServiceResult.Failure("لم يتم العثور على الطلب");
                }

                // Check if the order has direct route to supervisor
                var directRoutingInfo = await GetDirectRoutingInfoAsync(orderId);
                if (directRoutingInfo.IsSuccess && directRoutingInfo.Data.IsAvailable)
                {
                    // Apply direct route logic - submit order directly to supervisors
                    var supervisorsList = directRoutingInfo.Data.SupervisorsList;
                    var result = await SubmitOrderToSupervisorsAsync(order, "تم التوجيه السريع", supervisorsList, "التوجية السريع", OrderTransferTypes.Direct);

                    if (!result.IsSuccess)
                    {
                        return result;
                    }

                    // Update transfer type to direct
                    order.ConfirmedByAssistantManager = OrderHelper.ConfirmedBy(userName);

                    await _unitOfWork.Orders.UpdateAsync(order);
                    await _unitOfWork.SaveChangesAsync();

                    return ServiceResult.Success("تم تأكيد الطلب وتطبيق المسار السريع بنجاح");
                }
                else
                {
                    // No direct route available, proceed with normal flow
                    order.ConfirmedByAssistantManager = OrderHelper.ConfirmedBy(userName);
                    order.OrderStatus = OrderStatus.B;

                    await _unitOfWork.Orders.UpdateAsync(order);
                    await _unitOfWork.SaveChangesAsync();

                    return ServiceResult.Success("تم تأكيد الطلب وتحويله إلى منسق الموارد البشرية بنجاح");
                }
            }
            catch (Exception ex)
            {
                return ServiceResult.Failure($"خطأ في تأكيد الطلب: {ex.Message}");
            }
        }

        public async Task<ServiceResult> ReturnOrderToDirectManagerAsync(int orderId, string reason, string? userName)
        {
            try
            {
                if (orderId <= 0)
                {
                    return ServiceResult.Failure("رقم الطلب غير صحيح");
                }

                if (string.IsNullOrWhiteSpace(reason))
                {
                    return ServiceResult.Failure("سبب الإعادة مطلوب");
                }

                var order = await _unitOfWork.Orders.GetByIdAsync(orderId);
                if (order == null)
                {
                    return ServiceResult.Failure("لم يتم العثور على الطلب");
                }


                order.OrderStatus = OrderStatus.ReturnedByAssistantManager;
                order.ConfirmedByAssistantManager = OrderHelper.ReturnedBy(userName);
                order.ReasonForCancellation = reason;

                await _unitOfWork.Orders.UpdateAsync(order);
                await _unitOfWork.SaveChangesAsync();

                return ServiceResult.Success("تم إعادة الطلب إلى مدير القسم بنجاح");

            }
            catch (Exception ex)
            {
                return ServiceResult.Failure($"خطأ في إعادة الطلب: {ex.Message}");
            }
        }

        public async Task<ServiceResult> RejectOrderByAssistantManagerAsync(int orderId, string reason, string? userName)
        {
            try
            {
                if (orderId <= 0)
                {
                    return ServiceResult.Failure("رقم الطلب غير صحيح");
                }

                if (string.IsNullOrWhiteSpace(reason))
                {
                    return ServiceResult.Failure("سبب الإلغاء مطلوب");
                }

                var order = await _unitOfWork.Orders.GetByIdAsync(orderId);
                if (order == null)
                {
                    return ServiceResult.Failure("لم يتم العثور على الطلب");
                }

                string rejectedBy = userName;

                order.OrderStatus = OrderStatus.CancelledByAssistantManager;
                order.ConfirmedByAssistantManager = OrderHelper.RejectedBy(userName);
                order.ReasonForCancellation = reason;

                await _unitOfWork.Orders.UpdateAsync(order);
                await _unitOfWork.SaveChangesAsync();

                return ServiceResult.Success("تم إلغاء الطلب بنجاح");

            }
            catch (Exception ex)
            {
                return ServiceResult.Failure($"خطأ في إلغاء الطلب: {ex.Message}");
            }
        }

        #region HR Coordinator Methods

        public async Task<ServiceResult<List<OrderSummaryDto>>> GetHRCoordinatorOrdersAsync()
        {
            try
            {
                var orders = await _unitOfWork.Orders.GetHRCoordinatorPendingOrders();

                var orderSummaries = orders.Select(order => new OrderSummaryDto
                {
                    Id = order.Id,
                    EmployeeName = order.EmployeeName,
                    Department = order.Department,
                    OrderType = order.OrderType,
                    OrderStatus = order.OrderStatus,
                    CreatedAt = order.CreatedAt
                }).OrderByDescending(o => o.Id).ToList();

                return ServiceResult<List<OrderSummaryDto>>.Success(orderSummaries);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting HR coordinator orders");
                return ServiceResult<List<OrderSummaryDto>>.Failure("حدث خطأ أثناء جلب الطلبات");
            }
        }

        public async Task<ServiceResult<AutoRoutingInfoDto>> GetAutoRoutingInfoAsync(int orderId)
        {
            try
            {
                var order = await _unitOfWork.Orders.GetByIdAsync(orderId);
                if (order == null)
                {
                    return ServiceResult<AutoRoutingInfoDto>.Failure("لم يتم العثور على الطلب");
                }

                var nationality = ProccessNationality(order.Nationality);
                var autoRoute = await _unitOfWork.AutoRoutings.GetMatchingRouteAsync(
                    order.OrderType, nationality, order.JobTitle);

                var autoRoutingInfo = new AutoRoutingInfoDto();

                if (autoRoute != null && autoRoute.Status)
                {
                    autoRoutingInfo.IsAvailable = true;
                    autoRoutingInfo.SupervisorsList = autoRoute.Supervisors?.Split(';')
                        .Where(s => !string.IsNullOrWhiteSpace(s))
                        .Select(s => s.Trim())
                        .ToList() ?? new List<string>();

                    autoRoutingInfo.Message = BuildAutoPathMessage(autoRoutingInfo.SupervisorsList);
                }
                else
                {
                    autoRoutingInfo.IsAvailable = false;
                    autoRoutingInfo.Message = BuildNoAutoPathMessage();
                }

                return ServiceResult<AutoRoutingInfoDto>.Success(autoRoutingInfo);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting auto routing info for order {OrderId}", orderId);
                return ServiceResult<AutoRoutingInfoDto>.Failure("حدث خطأ أثناء جلب معلومات التوجيه التلقائي");
            }
        }

        public async Task<ServiceResult<DirectRoutingInfoDto>> GetDirectRoutingInfoAsync(int orderId)
        {
            try
            {
                var order = await _unitOfWork.Orders.GetByIdAsync(orderId);
                if (order == null)
                {
                    return ServiceResult<DirectRoutingInfoDto>.Failure("لم يتم العثور على الطلب");
                }

                var nationality = ProccessNationality(order.Nationality);
                var directRoute = await _unitOfWork.DirectRoutings.GetMatchingRouteAsync(
                    order.OrderType, nationality, order.JobTitle);

                var directRoutingInfo = new DirectRoutingInfoDto();

                if (directRoute != null && directRoute.Status)
                {
                    directRoutingInfo.IsAvailable = true;
                    directRoutingInfo.SupervisorsList = directRoute.Supervisors?.Split(';')
                        .Where(s => !string.IsNullOrWhiteSpace(s))
                        .Select(s => s.Trim())
                        .ToList() ?? new List<string>();

                    directRoutingInfo.Message = BuildDirectPathMessage(directRoutingInfo.SupervisorsList);
                }
                else
                {
                    directRoutingInfo.IsAvailable = false;
                    directRoutingInfo.Message = BuildNoDirectPathMessage();
                }

                return ServiceResult<DirectRoutingInfoDto>.Success(directRoutingInfo);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting direct routing info for order {OrderId}", orderId);
                return ServiceResult<DirectRoutingInfoDto>.Failure("حدث خطأ أثناء جلب معلومات المسار السريع");
            }
        }

        public async Task<ServiceResult> SubmitOrderByHrCoordinatorAsync(int orderId, string details, List<string> selectedSupervisors, string userName)
        {
            try
            {
                var order = await _unitOfWork.Orders.GetByIdAsync(orderId);
                if (order == null)
                {
                    return ServiceResult.Failure("لم يتم العثور على الطلب");
                }

                if (selectedSupervisors == null || selectedSupervisors.Count == 0)
                {
                    return ServiceResult.Failure("يجب تحديد مشرف واحد على الأقل");
                }

                var result = await SubmitOrderToSupervisorsAsync(order, details, selectedSupervisors, userName, OrderTransferTypes.Manually);
                if (!result.IsSuccess)
                {
                    return result;
                }

                await _unitOfWork.SaveChangesAsync();


                return ServiceResult.Success("تم تحويل الطلب للاعتماد من المشرفين بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error submitting order {OrderId} to supervisors", orderId);
                return ServiceResult.Failure("حدث خطأ أثناء تحويل الطلب");
            }
        }

        public async Task<ServiceResult> AutoRouteOrderAsync(int orderId, string userName)
        {
            try
            {
                var order = await _unitOfWork.Orders.GetByIdAsync(orderId);
                if (order == null)
                {
                    return ServiceResult.Failure("لم يتم العثور على الطلب");
                }

                var autoRoutingInfo = await GetAutoRoutingInfoAsync(orderId);
                if (!autoRoutingInfo.IsSuccess || !autoRoutingInfo.Data.IsAvailable)
                {
                    return ServiceResult.Failure("لا يوجد مسار تلقائي متاح لهذا الطلب");
                }

                // Apply auto routing
                var supervisorsList = autoRoutingInfo.Data.SupervisorsList;
                var result = await SubmitOrderToSupervisorsAsync(order, "تم التوجيه التلقائي", supervisorsList, userName, OrderTransferTypes.Auto);

                if (!result.IsSuccess)
                {
                    return result;
                }

                await _unitOfWork.SaveChangesAsync();

                return ServiceResult.Success("تم تطبيق التوجيه التلقائي بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error auto-routing order {OrderId}", orderId);
                return ServiceResult.Failure("حدث خطأ أثناء التوجيه التلقائي");
            }
        }

        public async Task<ServiceResult> RejectOrderByHRCoordinatorAsync(int orderId, string rejectReason, string userName)
        {
            try
            {
                var order = await _unitOfWork.Orders.GetByIdAsync(orderId);
                if (order == null)
                {
                    return ServiceResult.Failure("لم يتم العثور على الطلب");
                }

                order.OrderStatus = OrderStatus.CancelledByCoordinator;
                order.ConfirmedByCoordinator = OrderHelper.RejectedBy(userName);
                order.ReasonForCancellation = rejectReason;

                await _unitOfWork.Orders.UpdateAsync(order);
                await _unitOfWork.SaveChangesAsync();

                return ServiceResult.Success("تم إلغاء الطلب بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error rejecting order {OrderId} by HR coordinator", orderId);
                return ServiceResult.Failure("حدث خطأ أثناء إلغاء الطلب");
            }
        }

        public async Task<ServiceResult> DirectOrderToManagerAsync(int orderId, string details, string userName)
        {
            try
            {
                var order = await _unitOfWork.Orders.GetByIdAsync(orderId);
                if (order == null)
                {
                    return ServiceResult.Failure("لم يتم العثور على الطلب");
                }

                order.OrderStatus = OrderStatus.D;
                order.ConfirmedByCoordinator = OrderHelper.OrderDirectToManager(userName);
                order.CoordinatorDetails = string.IsNullOrEmpty(order.CoordinatorDetails) ? details : $"{order.CoordinatorDetails} | {details}";
                order.TransferType = OrderTransferTypes.DirectToManager;

                await _unitOfWork.Orders.UpdateAsync(order);
                await _unitOfWork.SaveChangesAsync();

                return ServiceResult.Success("تم تحويل الطلب مباشرة إلى مدير الموارد البشرية");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error directing order {OrderId} to manager", orderId);
                return ServiceResult.Failure("حدث خطأ أثناء تحويل الطلب للمدير");
            }
        }

        public async Task<ServiceResult<List<RestorableOrderDto>>> GetRestorableOrdersAsync(string searchTerm, string filter)
        {
            try
            {
                var orders = await _unitOfWork.Orders.GetRestorableOrdersAsync(searchTerm, filter);
                if (orders == null || orders.Count == 0)
                {
                    return ServiceResult<List<RestorableOrderDto>>.Failure("لا توجد طلبات قابلة للاستعادة");
                }

                return ServiceResult<List<RestorableOrderDto>>.Success(orders, $"تم العثور علي {orders.Count} طلبات قابلة للاستعادة");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting restorable orders");
                return ServiceResult<List<RestorableOrderDto>>.Failure("حدث خطأ أثناء جلب الطلبات القابلة للاستعادة");
            }
        }

        public async Task<ServiceResult<RestoreDetailsDto>> GetRestoreOrderDetailsAsync(int orderId)
        {
            try
            {
                var details = await _unitOfWork.Orders.GetRestoreOrderDetailsAsync(orderId);
                if (details == null)
                {
                    return ServiceResult<RestoreDetailsDto>.Failure("لم يتم العثور على تفاصيل الطلب");
                }

                return ServiceResult<RestoreDetailsDto>.Success(details);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting restore order details for {OrderId}", orderId);
                return ServiceResult<RestoreDetailsDto>.Failure("حدث خطأ أثناء جلب تفاصيل الطلب");
            }
        }

        public async Task<ServiceResult> RestoreOrderFromSupervisorsAsync(int orderId, string restoreNotes, string userName)
        {
            try
            {
                var order = await _unitOfWork.Orders.GetByIdAsync(orderId);
                if (order == null)
                {
                    return ServiceResult.Failure("لم يتم العثور على الطلب");
                }

                // Reset order to coordinator status
                order.OrderStatus = OrderStatus.B;
                order.ConfirmedByCoordinator = OrderHelper.RestoredBy(userName);
                order.CoordinatorDetails = (order.CoordinatorDetails ?? "") + $" | استعادة: {restoreNotes}";

                // Clear supervisor statuses
                await _unitOfWork.Orders.ClearSupervisorStatusesAsync(order);

                await _unitOfWork.Orders.UpdateAsync(order);
                await _unitOfWork.SaveChangesAsync();

                return ServiceResult.Success("تم استعادة الطلب بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error restoring order {OrderId}", orderId);
                return ServiceResult.Failure("حدث خطأ أثناء استعادة الطلب");
            }
        }

        public async Task<ServiceResult> ReturnOrderToAssistantManagerAsync(int orderId, string reason, string? userName)
        {
            try
            {
                if (orderId <= 0)
                {
                    return ServiceResult.Failure("رقم الطلب غير صحيح");
                }

                if (string.IsNullOrWhiteSpace(reason))
                {
                    return ServiceResult.Failure("سبب الإعادة مطلوب");
                }

                var order = await _unitOfWork.Orders.GetByIdAsync(orderId);
                if (order == null)
                {
                    return ServiceResult.Failure("لم يتم العثور على الطلب");
                }


                order.OrderStatus = OrderStatus.ReturnedByCoordinator;
                order.ConfirmedByCoordinator = OrderHelper.ReturnedBy(userName);
                order.ReasonForCancellation = reason;

                await _unitOfWork.Orders.UpdateAsync(order);
                await _unitOfWork.SaveChangesAsync();

                return ServiceResult.Success("تم إعادة الطلب إلى مساعد المدير بنجاح");

            }
            catch (Exception ex)
            {
                return ServiceResult.Failure($"خطأ في إعادة الطلب: {ex.Message}");
            }
        }

        public async Task<ServiceResult> OrderNeedsActionByCoordinatorAsync(int orderId, string actionDetails, string username)
        {
            if (orderId == 0)
                return ServiceResult.Failure("يرجى اختيار رقم الطلب.");
            if (string.IsNullOrWhiteSpace(actionDetails))
                return ServiceResult.Failure("يرجى إدخال الإجراءات المطلوبة.");

            var order = await _unitOfWork.Orders.GetOrderByIdAsync(orderId);
            if (order == null)
                return ServiceResult.Failure("لم يتم العثور على الطلب.");

            order.OrderStatus = OrderStatus.ActionRequired;
            order.ConfirmedByCoordinator = OrderHelper.OrderNeedActionByCoordinator(username);
            order.CoordinatorDetails = actionDetails;

            await _unitOfWork.Orders.UpdateAsync(order);
            await _unitOfWork.SaveChangesAsync();

            return ServiceResult.Success("تم حفظ طلب الإجراءات بنجاح.");
        }

        #endregion
        
        public async Task<ServiceResult<List<OrderPrintListItemDto>>> GetPrintableOrdersAsync(string searchTerm, string filter)
        {
            try
            {
                _logger.LogInformation("Getting printable orders with search term: {SearchTerm}, filter: {Filter}", searchTerm, filter);

                var orders = await _unitOfWork.Orders.GetAllAsync();

                // Apply search filter
                if (!string.IsNullOrWhiteSpace(searchTerm))
                {
                    orders = orders.Where(o =>
                        o.EmployeeName.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                        o.EmployeeNumber.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                        o.CivilRecord.Contains(searchTerm, StringComparison.OrdinalIgnoreCase)
                    ).ToList();
                }

                // Apply date filter
                var today = DateTime.Today;
                switch (filter?.ToLower())
                {
                    case "today":
                        orders = orders.Where(o => o.CreatedAt.Date == today).ToList();
                        break;
                    case "week":
                        var weekStart = today.AddDays(-(int)today.DayOfWeek);
                        orders = orders.Where(o => o.CreatedAt.Date >= weekStart).ToList();
                        break;
                    case "month":
                        orders = orders.Where(o => o.CreatedAt.Month == today.Month && o.CreatedAt.Year == today.Year).ToList();
                        break;
                    case "all":
                    default:
                        // No additional filtering
                        break;
                }

                var result = orders.Select(o => new OrderPrintListItemDto
                {
                    OrderId = o.Id,
                    OrderNumber = o.Id.ToString(),
                    EmployeeName = o.EmployeeName
                }).ToList();

                return ServiceResult<List<OrderPrintListItemDto>>.Success(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting printable orders");
                return ServiceResult<List<OrderPrintListItemDto>>.Failure($"خطأ في جلب الطلبات: {ex.Message}");
            }
        }

        public async Task<ServiceResult<OrderPrintDetailsDto>> GetOrderPrintDetailsAsync(int orderId)
        {
            try
            {
                _logger.LogInformation("Getting order print details for order ID: {OrderId}", orderId);

                if (orderId <= 0)
                {
                    return ServiceResult<OrderPrintDetailsDto>.Failure("رقم الطلب غير صحيح");
                }

                var order = await _unitOfWork.Orders.GetByIdAsync(orderId);
                if (order == null)
                {
                    return ServiceResult<OrderPrintDetailsDto>.Failure("لم يتم العثور على الطلب");
                }

                var details = new OrderPrintDetailsDto
                {
                    OrderId = order.Id,
                    OrderNumber = order.Id.ToString(),
                    OrderDate = order.CreatedAt.ToString("yyyy-MM-dd"),
                    OrderStatus = order.OrderStatus.ToDisplayString(),
                    OrderType = order.OrderType,
                    EmployeeName = order.EmployeeName,
                    Department = order.Department,
                    Notes = order.Details ?? string.Empty,
                    JobTitle = order.JobTitle,
                    EmployeeNumber = order.EmployeeNumber,
                    CivilRecord = order.CivilRecord,
                    Nationality = order.Nationality,
                    MobileNumber = order.MobileNumber,
                    EmploymentType = order.EmploymentType,
                    Qualification = order.Qualification,
                    ManagerApproval = order.ConfirmedByDepartmentManager ?? "-",
                    SupervisorApproval = order.ConfirmedByAssistantManager ?? "-",
                    CoordinatorApproval = order.ConfirmedByCoordinator ?? "-",
                    CancellationReason = order.ReasonForCancellation ?? "-",
                    CoordinatorDetails = order.CoordinatorDetails ?? "-",
                    HRManagerApproval = order.HumanResourcesManager ?? "-"
                };

                return ServiceResult<OrderPrintDetailsDto>.Success(details);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting order print details for order ID: {OrderId}", orderId);
                return ServiceResult<OrderPrintDetailsDto>.Failure($"خطأ في جلب تفاصيل الطلب: {ex.Message}");
            }
        }

        public async Task<ServiceResult<byte[]>> DownloadOrderAttachmentsZipAsync(int orderId)
        {
            try
            {
                _logger.LogInformation("Downloading attachments for order ID: {OrderId}", orderId);

                if (orderId <= 0)
                {
                    return ServiceResult<byte[]>.Failure("رقم الطلب غير صحيح");
                }

                var order = await _unitOfWork.Orders.GetByIdAsync(orderId);
                if (order == null)
                {
                    return ServiceResult<byte[]>.Failure("لم يتم العثور على الطلب");
                }

                var fileUrls = new List<string>();
                if (!string.IsNullOrEmpty(order.File1Url)) fileUrls.Add(order.File1Url);
                if (!string.IsNullOrEmpty(order.File2Url)) fileUrls.Add(order.File2Url);
                if (!string.IsNullOrEmpty(order.File3Url)) fileUrls.Add(order.File3Url);
                if (!string.IsNullOrEmpty(order.File4Url)) fileUrls.Add(order.File4Url);

                if (!fileUrls.Any())
                {
                    return ServiceResult<byte[]>.Failure("لا توجد مرفقات لهذا الطلب");
                }

                // Use FileService to create ZIP
                var zipResult = await _fileService.DownloadFilesZipAsync(fileUrls);
                if (!zipResult.IsSuccess)
                {
                    return ServiceResult<byte[]>.Failure(zipResult.Message);
                }

                return ServiceResult<byte[]>.Success(zipResult.Data, "تم تحميل المرفقات بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error downloading attachments for order ID: {OrderId}", orderId);
                return ServiceResult<byte[]>.Failure($"خطأ في تحميل المرفقات: {ex.Message}");
            }
        }

        public async Task<ServiceResult<byte[]>> GenerateOrderPdfAsync(int orderId)
        {
            try
            {
                _logger.LogInformation("Generating PDF for order ID: {OrderId}", orderId);

                if (orderId <= 0)
                {
                    return ServiceResult<byte[]>.Failure("رقم الطلب غير صحيح");
                }

                var order = await _unitOfWork.Orders.GetByIdAsync(orderId);
                if (order == null)
                {
                    return ServiceResult<byte[]>.Failure("لم يتم العثور على الطلب");
                }

                // Use FileService to generate PDF
                var pdfResult = await _fileService.GenerateOrderPdfAsync(order);
                if (!pdfResult.IsSuccess)
                {
                    return ServiceResult<byte[]>.Failure(pdfResult.Message);
                }

                return ServiceResult<byte[]>.Success(pdfResult.Data, "تم إنشاء ملف PDF بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating PDF for order ID: {OrderId}", orderId);
                return ServiceResult<byte[]>.Failure($"خطأ في إنشاء ملف PDF: {ex.Message}");
            }
        }

        public async Task<ServiceResult<List<OrderSummaryDto>>> GetSupervisorOrdersAsync(string supervisorRole)
        {
            try
            {
                var orders = await _unitOfWork.Orders.GetSupervisorOrdersAsync(supervisorRole);
                var orderSummaries = orders.Select(order => new OrderSummaryDto
                {
                    Id = order.Id,
                    EmployeeName = order.EmployeeName,
                    OrderType = order.OrderType,
                    Department = order.Department,
                    CreatedAt = order.CreatedAt,
                    OrderStatus = order.OrderStatus
                }).ToList();
                return ServiceResult<List<OrderSummaryDto>>.Success(orderSummaries);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting supervisor orders for role {SupervisorRole}", supervisorRole);
                return ServiceResult<List<OrderSummaryDto>>.Failure($"حدث خطأ أثناء جلب الطلبات: {ex.Message}");
            }
        }

        #region Private Helper Methods

        private ServiceResult ValidateOrderDto(OrderNewDto orderDto)
        {
            var errors = new List<string>();

            if (orderDto == null)
            {
                return ServiceResult.Failure("بيانات الطلب مطلوبة");
            }

            // Validate required fields
            if (string.IsNullOrWhiteSpace(orderDto.EmployeeName))
                errors.Add("اسم الموظف مطلوب");

            if (string.IsNullOrWhiteSpace(orderDto.JobTitle))
                errors.Add("الوظيفة مطلوبة");

            if (string.IsNullOrWhiteSpace(orderDto.EmployeeNumber))
                errors.Add("رقم الموظف مطلوب");

            if (string.IsNullOrWhiteSpace(orderDto.CivilRecord))
                errors.Add("السجل المدني مطلوب");

            if (string.IsNullOrWhiteSpace(orderDto.Nationality))
                errors.Add("الجنسية مطلوبة");

            if (string.IsNullOrWhiteSpace(orderDto.MobileNumber))
                errors.Add("رقم الجوال مطلوب");

            if (string.IsNullOrWhiteSpace(orderDto.Department))
                errors.Add("القسم مطلوب");

            if (string.IsNullOrWhiteSpace(orderDto.EmploymentType))
                errors.Add("نوع التوظيف مطلوب");

            if (string.IsNullOrWhiteSpace(orderDto.Qualification))
                errors.Add("المؤهل مطلوب");

            if (string.IsNullOrWhiteSpace(orderDto.OrderType))
                errors.Add("نوع الطلب مطلوب");

            // Validate mobile number format
            if (!string.IsNullOrWhiteSpace(orderDto.MobileNumber) &&
                !Regex.IsMatch(orderDto.MobileNumber, @"^[0-9+\-\s()]+$"))
            {
                errors.Add("صيغة رقم الجوال غير صحيحة");
            }

            // Validate attachments count
            if (orderDto.Attachments?.Count > _options.MaxAttachments)
            {
                errors.Add($"الحد الأقصى للمرفقات هو {_options.MaxAttachments}");
            }

            return errors.Any()
                ? ServiceResult.Failure("فشل في التحقق من صحة البيانات", errors)
                : ServiceResult.Success();
        }

        private List<AttachmentDto> BuildAttachmentList(OrdersTable order)
        {
            var attachments = new List<AttachmentDto>();

            if (!string.IsNullOrEmpty(order.File1Url))
                attachments.Add(new AttachmentDto { FileName = "ملف 1", Uploaded = true, FileUrl = order.File1Url });
            if (!string.IsNullOrEmpty(order.File2Url))
                attachments.Add(new AttachmentDto { FileName = "ملف 2", Uploaded = true, FileUrl = order.File2Url });
            if (!string.IsNullOrEmpty(order.File3Url))
                attachments.Add(new AttachmentDto { FileName = "ملف 3", Uploaded = true, FileUrl = order.File3Url });
            if (!string.IsNullOrEmpty(order.File4Url))
                attachments.Add(new AttachmentDto { FileName = "ملف 4", Uploaded = true, FileUrl = order.File4Url });

            return attachments;
        }

        private async Task<ServiceResult> SubmitOrderToSupervisorsAsync(OrdersTable order, string details, List<string> selectedSupervisors, string coordinatorName, string orderTransferType)
        {
            try
            {
                if (order == null)
                {
                    return ServiceResult.Failure("لم يتم العثور على الطلب");
                }

                // Update order status and coordinator details
                order.OrderStatus = OrderStatus.C;
                order.ConfirmedByCoordinator = OrderHelper.ConfirmedBy(coordinatorName);
                order.CoordinatorDetails = string.IsNullOrEmpty(order.CoordinatorDetails) ? details : $"{order.CoordinatorDetails} | {details}";
                order.TransferType = orderTransferType;

                // Update selected supervisors
                if (selectedSupervisors == null || selectedSupervisors.Count == 0)
                {
                    return ServiceResult.Failure("يجب تحديد مشرفين لتحويل الطلب");
                }
                var statusWithDate = OrderHelper.OrderUnderImplementation();

                await _unitOfWork.Orders.UpdateSupervisorStatusesAsync(order, selectedSupervisors, statusWithDate);

                await _unitOfWork.Orders.UpdateAsync(order);

                return ServiceResult.Success("تم تحويل الطلب للاعتماد من المشرفين بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error submitting order {OrderId} to supervisors", order.Id);
                return ServiceResult.Failure("حدث خطأ أثناء تحويل الطلب");
            }
        }

        #endregion

        #region Helper Methods

        private string ProccessNationality(string nationality)
        {

            if (nationality != "سعودي")
            {
                return "غير سعودي";
            }

            return nationality;
        }

        private string BuildAutoPathMessage(List<string> supervisors)
        {
            var message = @"<div class='alert alert-info alert-dismissible text-right' role='alert'>
                <button type='button' class='btn-close' data-bs-dismiss='alert' aria-label='Close'></button>
                <h5 class='alert-heading'>✨ متوفر مسار تلقائي لهذا الطلب</h5>
                <hr><p class='mb-0'>سيتم توجيه الطلب إلى:</p>
                <ul class='list-unstyled mt-2'>";
            foreach (string supervisor in supervisors)
            {
                message += $"<li><i class='fas fa-check-circle text-success'></i> {supervisor.Trim()}</li>";
            }
            message += @"</ul><hr>
                <small class='text-muted'>يمكنك النقر على زر 'التوجيه التلقائي' لتطبيق هذا المسار</small>
                </div>";    
            return message;
        }

        private string BuildNoAutoPathMessage()
        {
            return @"<div class='alert alert-secondary alert-dismissible text-right' role='alert'>
                <button type='button' class='btn-close' data-bs-dismiss='alert' aria-label='Close'></button>
                <h5 class='alert-heading'>ℹ️ لا يوجد مسار تلقائي</h5>
                <p class='mb-0'>هذا الطلب يحتاج إلى توجيه يدوي</p></div>";  
        }

        private string BuildDirectPathMessage(List<string> supervisors)
        {
            var message = @"<div class='alert alert-success alert-dismissible text-right' role='alert'>
                <button type='button' class='btn-close' data-bs-dismiss='alert' aria-label='Close'></button>
                <h5 class='alert-heading'>🚀 متوفر مسار سريع لهذا الطلب</h5>
                <hr><p class='mb-0'>سيتم توجيه الطلب مباشرة إلى:</p>
                <ul class='list-unstyled mt-2'>";
            foreach (string supervisor in supervisors)
            {
                message += $"<li><i class='fas fa-bolt text-warning'></i> {supervisor.Trim()}</li>";
            }
            message += @"</ul><hr>
                <small class='text-muted'>سيتم تطبيق هذا المسار تلقائياً عند تأكيد الطلب</small>
                </div>";    
            return message;
        }

        private string BuildNoDirectPathMessage()
        {
            return @"<div class='alert alert-info alert-dismissible text-right' role='alert'>
                <button type='button' class='btn-close' data-bs-dismiss='alert' aria-label='Close'></button>
                <h5 class='alert-heading'>ℹ️ لا يوجد مسار سريع</h5>
                <p class='mb-0'>سيتم تحويل الطلب إلى منسق الموارد البشرية</p></div>";  
        }

        #endregion
    }
}