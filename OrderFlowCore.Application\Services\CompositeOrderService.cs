using OrderFlowCore.Application.Common;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Core.Entities;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace OrderFlowCore.Application.Services
{
    /// <summary>
    /// Composite implementation of IOrderService that delegates to specialized services.
    /// This maintains the existing interface while providing clean separation of concerns.
    /// </summary>
    public class CompositeOrderService : IOrderService
    {
        private readonly IOrderManagementService _orderManagementService;
        private readonly IDirectManagerOrderService _directManagerService;
        private readonly IAssistantManagerOrderService _assistantManagerService;
        private readonly IHRCoordinatorOrderService _hrCoordinatorService;
        private readonly ISupervisorOrderService _supervisorService;
        private readonly IOrderPrintService _orderPrintService;
        private readonly IOrderRestorationService _orderRestorationService;

        public CompositeOrderService(
            IOrderManagementService orderManagementService,
            IDirectManagerOrderService directManagerService,
            IAssistantManagerOrderService assistantManagerService,
            IHRCoordinatorOrderService hrCoordinatorService,
            ISupervisorOrderService supervisorService,
            IOrderPrintService orderPrintService,
            IOrderRestorationService orderRestorationService)
        {
            _orderManagementService = orderManagementService ?? throw new ArgumentNullException(nameof(orderManagementService));
            _directManagerService = directManagerService ?? throw new ArgumentNullException(nameof(directManagerService));
            _assistantManagerService = assistantManagerService ?? throw new ArgumentNullException(nameof(assistantManagerService));
            _hrCoordinatorService = hrCoordinatorService ?? throw new ArgumentNullException(nameof(hrCoordinatorService));
            _supervisorService = supervisorService ?? throw new ArgumentNullException(nameof(supervisorService));
            _orderPrintService = orderPrintService ?? throw new ArgumentNullException(nameof(orderPrintService));
            _orderRestorationService = orderRestorationService ?? throw new ArgumentNullException(nameof(orderRestorationService));
        }

        #region Core Order Management

        public async Task<ServiceResult<DropdownDataDto>> GetDropdownDataAsync()
        {
            return await _orderManagementService.GetDropdownDataAsync();
        }

        public async Task<ServiceResult<OrderSummaryDto>> CreateOrderAsync(OrderNewDto orderDto)
        {
            return await _orderManagementService.CreateOrderAsync(orderDto);
        }

        public async Task<ServiceResult<OrderDetailsDto>> GetOrderDetailsAsync(int orderId)
        {
            return await _orderManagementService.GetOrderDetailsAsync(orderId);
        }

        public async Task<ServiceResult<OrderDetailsDto>> GetOrderDetailsByIdAndCivilRecordAsync(int orderId, string civilRecord)
        {
            return await _orderManagementService.GetOrderDetailsByIdAndCivilRecordAsync(orderId, civilRecord);
        }

        public async Task<ServiceResult> UploadAttachmentAsync(int orderId, byte[] fileData, string fileName, int fileNumber)
        {
            return await _orderManagementService.UploadAttachmentAsync(orderId, fileData, fileName, fileNumber);
        }

        public async Task<ServiceResult<byte[]>> DownloadAttachmentsZipAsync(int orderId)
        {
            return await _orderManagementService.DownloadAttachmentsZipAsync(orderId);
        }

        #endregion

        #region Direct Manager Methods

        public async Task<ServiceResult<List<OrderSummaryDto>>> GetPendingOrdersForDirectMangerAsync()
        {
            return await _directManagerService.GetPendingOrdersForDirectMangerAsync();
        }

        public async Task<ServiceResult> ConfirmOrderByDirectManagerAsync(int orderId, string? userName)
        {
            return await _directManagerService.ConfirmOrderByDirectManagerAsync(orderId, userName);
        }

        public async Task<ServiceResult> RejectOrderByDirectManagerAsync(int orderId, string reason, string? userName)
        {
            return await _directManagerService.RejectOrderByDirectManagerAsync(orderId, reason, userName);
        }

        #endregion

        #region Assistant Manager Methods

        public async Task<ServiceResult<List<OrderSummaryDto>>> GetAssistantManagerOrdersAsync(AssistantManagerType assistantManagerId)
        {
            return await _assistantManagerService.GetAssistantManagerOrdersAsync(assistantManagerId);
        }

        public async Task<ServiceResult> ConfirmOrderByAssistantManagerAsync(int orderId, string? userName)
        {
            return await _assistantManagerService.ConfirmOrderByAssistantManagerAsync(orderId, userName);
        }

        public async Task<ServiceResult> ReturnOrderToDirectManagerAsync(int orderId, string reason, string? userName)
        {
            return await _assistantManagerService.ReturnOrderToDirectManagerAsync(orderId, reason, userName);
        }

        public async Task<ServiceResult> RejectOrderByAssistantManagerAsync(int orderId, string reason, string? userName)
        {
            return await _assistantManagerService.RejectOrderByAssistantManagerAsync(orderId, reason, userName);
        }

        #endregion

        #region HR Coordinator Methods

        public async Task<ServiceResult<List<OrderSummaryDto>>> GetHRCoordinatorOrdersAsync()
        {
            return await _hrCoordinatorService.GetHRCoordinatorOrdersAsync();
        }

        public async Task<ServiceResult<AutoRoutingInfoDto>> GetAutoRoutingInfoAsync(int orderId)
        {
            return await _hrCoordinatorService.GetAutoRoutingInfoAsync(orderId);
        }

        public async Task<ServiceResult<DirectRoutingInfoDto>> GetDirectRoutingInfoAsync(int orderId)
        {
            return await _hrCoordinatorService.GetDirectRoutingInfoAsync(orderId);
        }

        public async Task<ServiceResult> SubmitOrderByHrCoordinatorAsync(int orderId, string details, List<string> selectedSupervisors, string userName)
        {
            return await _hrCoordinatorService.SubmitOrderByHrCoordinatorAsync(orderId, details, selectedSupervisors, userName);
        }

        public async Task<ServiceResult> AutoRouteOrderAsync(int orderId, string userName)
        {
            return await _hrCoordinatorService.AutoRouteOrderAsync(orderId, userName);
        }

        public async Task<ServiceResult> RejectOrderByHRCoordinatorAsync(int orderId, string rejectReason, string userName)
        {
            return await _hrCoordinatorService.RejectOrderByHRCoordinatorAsync(orderId, rejectReason, userName);
        }

        public async Task<ServiceResult> DirectOrderToManagerAsync(int orderId, string details, string userName)
        {
            return await _hrCoordinatorService.DirectOrderToManagerAsync(orderId, details, userName);
        }

        public async Task<ServiceResult> ReturnOrderToAssistantManagerAsync(int orderId, string reason, string? userName)
        {
            return await _hrCoordinatorService.ReturnOrderToAssistantManagerAsync(orderId, reason, userName);
        }

        public async Task<ServiceResult> OrderNeedsActionByCoordinatorAsync(int orderId, string actionRequired, string username)
        {
            return await _hrCoordinatorService.OrderNeedsActionByCoordinatorAsync(orderId, actionRequired, username);
        }

        #endregion

        #region Supervisor Methods

        public async Task<ServiceResult<List<OrderSummaryDto>>> GetSupervisorOrdersAsync(string supervisorRole)
        {
            return await _supervisorService.GetSupervisorOrdersAsync(supervisorRole);
        }

        public async Task<ServiceResult> ConfirmOrderBySupervisorAsync(int orderId, string supervisorRole, string userName)
        {
            return await _supervisorService.ConfirmOrderBySupervisorAsync(orderId, supervisorRole, userName);
        }

        public async Task<ServiceResult> NeedsActionBySupervisorAsync(int orderId, string actionRequired, string supervisorRole, string userName)
        {
            return await _supervisorService.NeedsActionBySupervisorAsync(orderId, actionRequired, supervisorRole, userName);
        }

        public async Task<ServiceResult> RejectOrderBySupervisorAsync(int orderId, string rejectReason, string supervisorRole, string userName)
        {
            return await _supervisorService.RejectOrderBySupervisorAsync(orderId, rejectReason, supervisorRole, userName);
        }

        public async Task<ServiceResult<List<OrderSummaryDto>>> GetSupervisorOrdersAsync(string supervisorRole)
        {
            return await _supervisorService.GetSupervisorOrdersAsync(supervisorRole);
        }

        #endregion

        #region Print Methods

        public async Task<ServiceResult<List<OrderPrintListItemDto>>> GetPrintableOrdersAsync(string searchTerm, string filter)
        {
            return await _orderPrintService.GetPrintableOrdersAsync(searchTerm, filter);
        }

        public async Task<ServiceResult<OrderPrintDetailsDto>> GetOrderPrintDetailsAsync(int orderId)
        {
            return await _orderPrintService.GetOrderPrintDetailsAsync(orderId);
        }

        public async Task<ServiceResult<byte[]>> DownloadOrderAttachmentsZipAsync(int orderId)
        {
            return await _orderPrintService.DownloadOrderAttachmentsZipAsync(orderId);
        }

        public async Task<ServiceResult<byte[]>> GenerateOrderPdfAsync(int orderId)
        {
            return await _orderPrintService.GenerateOrderPdfAsync(orderId);
        }

        #endregion

        #region Restoration Methods

        public async Task<ServiceResult<List<RestorableOrderDto>>> GetRestorableOrdersAsync(string searchTerm, string filter)
        {
            return await _orderRestorationService.GetRestorableOrdersAsync(searchTerm, filter);
        }

        public async Task<ServiceResult<RestoreDetailsDto>> GetRestoreOrderDetailsAsync(int orderId)
        {
            return await _orderRestorationService.GetRestoreOrderDetailsAsync(orderId);
        }

        public async Task<ServiceResult> RestoreOrderFromSupervisorsAsync(int orderId, string restoreNotes, string userName)
        {
            return await _orderRestorationService.RestoreOrderFromSupervisorsAsync(orderId, restoreNotes, userName);
        }

        #endregion
    }
}
