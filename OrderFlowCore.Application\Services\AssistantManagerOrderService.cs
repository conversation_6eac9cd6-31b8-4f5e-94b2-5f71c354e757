using Microsoft.Extensions.Logging;
using OrderFlowCore.Application.Common;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Helper;
using OrderFlowCore.Application.Interfaces.Repositories;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Core.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace OrderFlowCore.Application.Services
{
    public interface IAssistantManagerOrderService
    {
        Task<ServiceResult<List<OrderSummaryDto>>> GetAssistantManagerOrdersAsync(AssistantManagerType assistantManagerId);
        Task<ServiceResult> ConfirmOrderByAssistantManagerAsync(int orderId, string? userName);
        Task<ServiceResult> ReturnOrderToDirectManagerAsync(int orderId, string reason, string? userName);
        Task<ServiceResult> RejectOrderByAssistantManagerAsync(int orderId, string reason, string? userName);
    }

    public class AssistantManagerOrderService : IAssistantManagerOrderService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<AssistantManagerOrderService> _logger;

        public AssistantManagerOrderService(
            IUnitOfWork unitOfWork,
            ILogger<AssistantManagerOrderService> logger)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public async Task<ServiceResult<List<OrderSummaryDto>>> GetAssistantManagerOrdersAsync(AssistantManagerType assistantManagerId)
        {
            try
            {
                if (assistantManagerId == AssistantManagerType.Unknown)
                {
                    return ServiceResult<List<OrderSummaryDto>>.Failure("معرف مساعد المدير مطلوب");
                }

                // Get orders that are assigned to this assistant manager
                var assistantManagerOrders = await _unitOfWork.Orders.GetAssistantManagerOrdersAsync(assistantManagerId);

                var orderSummaries = assistantManagerOrders.Select(order => new OrderSummaryDto
                {
                    Id = order.Id,
                    EmployeeName = order.EmployeeName,
                    OrderType = order.OrderType,
                    Department = order.Department,
                    CreatedAt = order.CreatedAt,
                    OrderStatus = order.OrderStatus
                }).ToList();

                return ServiceResult<List<OrderSummaryDto>>.Success(orderSummaries);
            }
            catch (Exception ex)
            {
                return ServiceResult<List<OrderSummaryDto>>.Failure($"خطأ في جلب طلبات مساعد المدير: {ex.Message}");
            }
        }

        public async Task<ServiceResult> ConfirmOrderByAssistantManagerAsync(int orderId, string? userName)
        {
            try
            {
                if (orderId <= 0)
                {
                    return ServiceResult.Failure("رقم الطلب غير صحيح");
                }

                var order = await _unitOfWork.Orders.GetByIdAsync(orderId);
                if (order == null)
                {
                    return ServiceResult.Failure("لم يتم العثور على الطلب");
                }

                // Check if the order has direct route to supervisor
                var directRoutingInfo = await GetDirectRoutingInfoAsync(orderId);
                if (directRoutingInfo.IsSuccess && directRoutingInfo.Data.IsAvailable)
                {
                    // Apply direct route logic - submit order directly to supervisors
                    var supervisorsList = directRoutingInfo.Data.SupervisorsList;
                    var result = await SubmitOrderToSupervisorsAsync(order, "تم التوجيه السريع", supervisorsList, "التوجية السريع", OrderTransferTypes.Direct);

                    if (!result.IsSuccess)
                    {
                        return result;
                    }

                    // Update transfer type to direct
                    order.ConfirmedByAssistantManager = OrderHelper.ConfirmedBy(userName);

                    await _unitOfWork.Orders.UpdateAsync(order);
                    await _unitOfWork.SaveChangesAsync();

                    return ServiceResult.Success("تم تأكيد الطلب وتطبيق المسار السريع بنجاح");
                }
                else
                {
                    // No direct route available, proceed with normal flow
                    order.ConfirmedByAssistantManager = OrderHelper.ConfirmedBy(userName);
                    order.OrderStatus = OrderStatus.B;

                    await _unitOfWork.Orders.UpdateAsync(order);
                    await _unitOfWork.SaveChangesAsync();

                    return ServiceResult.Success("تم تأكيد الطلب وتحويله إلى منسق الموارد البشرية بنجاح");
                }
            }
            catch (Exception ex)
            {
                return ServiceResult.Failure($"خطأ في تأكيد الطلب: {ex.Message}");
            }
        }

        public async Task<ServiceResult> ReturnOrderToDirectManagerAsync(int orderId, string reason, string? userName)
        {
            try
            {
                if (orderId <= 0)
                {
                    return ServiceResult.Failure("رقم الطلب غير صحيح");
                }

                if (string.IsNullOrWhiteSpace(reason))
                {
                    return ServiceResult.Failure("سبب الإعادة مطلوب");
                }

                var order = await _unitOfWork.Orders.GetByIdAsync(orderId);
                if (order == null)
                {
                    return ServiceResult.Failure("لم يتم العثور على الطلب");
                }

                order.OrderStatus = OrderStatus.ReturnedByAssistantManager;
                order.ConfirmedByAssistantManager = OrderHelper.ReturnedBy(userName);
                order.ReasonForCancellation = reason;

                await _unitOfWork.Orders.UpdateAsync(order);
                await _unitOfWork.SaveChangesAsync();

                return ServiceResult.Success("تم إعادة الطلب إلى مدير القسم بنجاح");
            }
            catch (Exception ex)
            {
                return ServiceResult.Failure($"خطأ في إعادة الطلب: {ex.Message}");
            }
        }

        public async Task<ServiceResult> RejectOrderByAssistantManagerAsync(int orderId, string reason, string? userName)
        {
            try
            {
                if (orderId <= 0)
                {
                    return ServiceResult.Failure("رقم الطلب غير صحيح");
                }

                if (string.IsNullOrWhiteSpace(reason))
                {
                    return ServiceResult.Failure("سبب الإلغاء مطلوب");
                }

                var order = await _unitOfWork.Orders.GetByIdAsync(orderId);
                if (order == null)
                {
                    return ServiceResult.Failure("لم يتم العثور على الطلب");
                }

                order.OrderStatus = OrderStatus.CancelledByAssistantManager;
                order.ConfirmedByAssistantManager = OrderHelper.RejectedBy(userName);
                order.ReasonForCancellation = reason;

                await _unitOfWork.Orders.UpdateAsync(order);
                await _unitOfWork.SaveChangesAsync();

                return ServiceResult.Success("تم إلغاء الطلب بنجاح");
            }
            catch (Exception ex)
            {
                return ServiceResult.Failure($"خطأ في إلغاء الطلب: {ex.Message}");
            }
        }

        #region Private Helper Methods

        private async Task<ServiceResult<DirectRoutingInfoDto>> GetDirectRoutingInfoAsync(int orderId)
        {
            try
            {
                var order = await _unitOfWork.Orders.GetByIdAsync(orderId);
                if (order == null)
                {
                    return ServiceResult<DirectRoutingInfoDto>.Failure("لم يتم العثور على الطلب");
                }

                var nationality = ProcessNationality(order.Nationality);
                var directRoute = await _unitOfWork.DirectRoutings.GetMatchingRouteAsync(
                    order.OrderType, nationality, order.JobTitle);

                var directRoutingInfo = new DirectRoutingInfoDto();

                if (directRoute != null && directRoute.Status)
                {
                    directRoutingInfo.IsAvailable = true;
                    directRoutingInfo.SupervisorsList = directRoute.Supervisors?.Split(';')
                        .Where(s => !string.IsNullOrWhiteSpace(s))
                        .Select(s => s.Trim())
                        .ToList() ?? new List<string>();
                }
                else
                {
                    directRoutingInfo.IsAvailable = false;
                }

                return ServiceResult<DirectRoutingInfoDto>.Success(directRoutingInfo);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting direct routing info for order {OrderId}", orderId);
                return ServiceResult<DirectRoutingInfoDto>.Failure("حدث خطأ أثناء جلب معلومات المسار السريع");
            }
        }

        private async Task<ServiceResult> SubmitOrderToSupervisorsAsync(Core.Models.OrdersTable order, string details, List<string> selectedSupervisors, string coordinatorName, string orderTransferType)
        {
            try
            {
                if (order == null)
                {
                    return ServiceResult.Failure("لم يتم العثور على الطلب");
                }

                // Update order status and coordinator details
                order.OrderStatus = OrderStatus.C;
                order.ConfirmedByCoordinator = OrderHelper.ConfirmedBy(coordinatorName);
                order.CoordinatorDetails = string.IsNullOrEmpty(order.CoordinatorDetails) ? details : $"{order.CoordinatorDetails} | {details}";
                order.TransferType = orderTransferType;

                // Update selected supervisors
                if (selectedSupervisors == null || selectedSupervisors.Count == 0)
                {
                    return ServiceResult.Failure("يجب تحديد مشرفين لتحويل الطلب");
                }
                var statusWithDate = OrderHelper.OrderUnderImplementation();

                await _unitOfWork.Orders.UpdateSupervisorStatusesAsync(order, selectedSupervisors, statusWithDate);

                await _unitOfWork.Orders.UpdateAsync(order);

                return ServiceResult.Success("تم تحويل الطلب للاعتماد من المشرفين بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error submitting order {OrderId} to supervisors", order.Id);
                return ServiceResult.Failure("حدث خطأ أثناء تحويل الطلب");
            }
        }

        private string ProcessNationality(string nationality)
        {
            if (nationality != "سعودي")
            {
                return "غير سعودي";
            }

            return nationality;
        }

        #endregion
    }
}
