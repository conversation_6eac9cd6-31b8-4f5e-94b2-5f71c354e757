using System.Collections.Generic;
using System.Threading.Tasks;
using OrderFlowCore.Application.DTOs;

namespace OrderFlowCore.Application.Interfaces.Services
{
    public interface ISupervisorsFollowUpService
    {
        Task<List<SupervisorsFollowUpDto>> GetBySupervisorAsync(string supervisorId);
        Task<SupervisorsFollowUpDto> GetAsync(string supervisorId, string civilRecord);
        Task AddAsync(SupervisorsFollowUpDto record);
        Task UpdateAsync(SupervisorsFollowUpDto record);
        Task DeleteAsync(string supervisorId, string civilRecord);
        Task DeleteAllAsync(string supervisorId);
        Task<int> ImportAsync(string supervisorId, System.IO.Stream csvStream);
        Task<byte[]> ExportAsync(string supervisorId);
    }
} 