using Microsoft.AspNetCore.Mvc;
using OrderFlowCore.Web.ViewModels;
using Microsoft.AspNetCore.Mvc.Rendering;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.Linq;
using System;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Application.DTOs;
using System.Security.Claims;

namespace OrderFlowCore.Web.Controllers
{
    public class SupervisorOrdersController : Controller
    {
        private readonly ISupervisorOrderService _supervisorOrderService;
        private readonly IOrderManagementService _orderManagementService;
        private readonly ISupervisorsFollowUpService _followUpService;

        public SupervisorOrdersController(ISupervisorOrderService supervisorOrderService, IOrderManagementService orderManagementService, ISupervisorsFollowUpService followUpService)
        {
            _supervisorOrderService = supervisorOrderService;
            _orderManagementService = orderManagementService;
            _followUpService = followUpService;
        }

        [HttpGet]
        public async Task<IActionResult> Index()
        {
            var viewModel = new SupervisorOrdersViewModel { };

            // Load order numbers for dropdown for supervisors
            var supervisorRole = User.FindFirst("role")?.Value ?? User.Identity?.Name;
            var ordersResult = await _supervisorOrderService.GetSupervisorOrdersAsync(supervisorRole);
            if (ordersResult.IsSuccess)
            {
                viewModel.OrderNumbers = ordersResult.Data.Select(o => new SelectListItem
                {
                    Value = o.Id.ToString(),
                    Text = string.IsNullOrEmpty(o.EmployeeName) ? o.Id.ToString() : $"{o.Id} | {o.EmployeeName}"
                }).ToList();
            }
            else
            {
                viewModel.ErrorMessage = ordersResult.Message;
            }

            return View(viewModel);
        }

        [HttpPost]
        public async Task<IActionResult> GetOrderDetails(int orderId)
        {
            var result = await _orderManagementService.GetOrderDetailsAsync(orderId);
            if (!result.IsSuccess)
            {
                return Json(new { success = false, message = result.Message });
            }

            return Json(new { success = true, data = result.Data });
        }

        [HttpGet]
        public async Task<IActionResult> ProcessOrder(int id)
        {
            var viewModel = new SupervisorOrdersViewModel
            {
                SelectedOrderId = id,
            };

            var detailsResult = await _orderManagementService.GetOrderDetailsAsync(id);
            if (!detailsResult.IsSuccess)
            {
                viewModel.ErrorMessage = detailsResult.Message;
            }

            return View("ProcessOrder", viewModel);
        }

        [HttpGet]
        public async Task<IActionResult> FollowUpRecords(string successMessage = null, string errorMessage = null)
        {
            var viewModel = new SupervisorOrdersViewModel
            {
                SuccessMessage = successMessage,
                ErrorMessage = errorMessage
            };

            // Load follow-up records for this supervisor
            var supervisorId = User.Identity?.Name;
            if (!string.IsNullOrEmpty(supervisorId))
            {
                viewModel.FollowUpRecords = await _followUpService.GetBySupervisorAsync(supervisorId);
            }

            return View("FollowUpRecords", viewModel);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ConfirmOrder(int orderId)
        {
            var supervisorRole = User.FindFirst("role")?.Value ?? User.Identity?.Name;
            var userName = User.Identity?.Name;
            if (orderId <= 0)
                return Json(new { success = false, message = "رقم الطلب غير صالح" });
            var result = await _supervisorOrderService.ConfirmOrderBySupervisorAsync(orderId, supervisorRole, userName);
            return Json(new { success = result.IsSuccess, message = result.Message });
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> NeedsAction(int orderId, string actionRequired)
        {
            var supervisorRole = User.FindFirst("role")?.Value ?? User.Identity?.Name;
            var userName = User.Identity?.Name;
            if (orderId <= 0)
                return Json(new { success = false, message = "رقم الطلب غير صالح" });
            if (string.IsNullOrWhiteSpace(actionRequired))
                return Json(new { success = false, message = "يرجى إدخال تفاصيل الإجراء المطلوب" });
            var result = await _supervisorOrderService.NeedsActionBySupervisorAsync(orderId, actionRequired, supervisorRole, userName);
            return Json(new { success = result.IsSuccess, message = result.Message });
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> RejectOrder(int orderId, string rejectReason)
        {
            var supervisorRole = User.FindFirst("role")?.Value ?? User.Identity?.Name;
            var userName = User.Identity?.Name;
            if (orderId <= 0)
                return Json(new { success = false, message = "رقم الطلب غير صالح" });
            if (string.IsNullOrWhiteSpace(rejectReason))
                return Json(new { success = false, message = "يرجى إدخال سبب الإعادة" });
            var result = await _supervisorOrderService.RejectOrderBySupervisorAsync(orderId, rejectReason, supervisorRole, userName);
            return Json(new { success = result.IsSuccess, message = result.Message });
        }

        [HttpGet]
        public async Task<IActionResult> DownloadAttachments(int orderId)
        {
            var result = await _orderManagementService.DownloadAttachmentsZipAsync(orderId);
            if (!result.IsSuccess)
            {
                return RedirectToAction(nameof(Index), new { selectedOrderId = orderId, errorMessage = result.Message });
            }
            var detailsResult = await _orderManagementService.GetOrderDetailsAsync(orderId);
            var fileName = detailsResult.IsSuccess ? $"مرفقات_طلب_{orderId}_{detailsResult.Data.EmployeeName}.zip" : $"مرفقات_طلب_{orderId}.zip";
            return File(result.Data, "application/zip", fileName);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ImportFollowUpRecords()
        {
            var supervisorId = User.Identity?.Name;
            var file = Request.Form.Files["csvFile"];
            if (file == null || file.Length == 0)
            {
                return RedirectToAction(nameof(Index), new { showRecordsPanel = true, errorMessage = "يرجى اختيار ملف CSV صالح" });
            }
            using (var stream = file.OpenReadStream())
            {
                var count = await _followUpService.ImportAsync(supervisorId, stream);
                return RedirectToAction(nameof(Index), new { showRecordsPanel = true, successMessage = $"تم استيراد {count} سجل بنجاح" });
            }
        }

        [HttpGet]
        public async Task<IActionResult> ExportFollowUpRecords()
        {
            var supervisorId = User.Identity?.Name;
            var data = await _followUpService.ExportAsync(supervisorId);
            return File(data, "text/csv", "FollowUpRecords.csv");
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteAllFollowUpRecords()
        {
            var supervisorId = User.Identity?.Name;
            await _followUpService.DeleteAllAsync(supervisorId);
            return RedirectToAction(nameof(Index), new { showRecordsPanel = true, successMessage = "تم حذف جميع السجلات بنجاح" });
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> AddFollowUpRecordAjax([FromBody] SupervisorsFollowUpDto dto)
        {
            try
            {
                dto.SupervisorId = User.Identity?.Name;
                await _followUpService.AddAsync(dto);
                return Json(new { success = true, message = "تمت إضافة السجل بنجاح" });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = ex.Message });
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> EditFollowUpRecordAjax([FromBody] SupervisorsFollowUpDto dto)
        {
            try
            {
                dto.SupervisorId = User.Identity?.Name;
                await _followUpService.UpdateAsync(dto);
                return Json(new { success = true, message = "تم تحديث السجل بنجاح" });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = ex.Message });
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteFollowUpRecordAjax([FromBody] SupervisorsFollowUpDto dto)
        {
            try
            {
                var supervisorId = User.Identity?.Name;
                await _followUpService.DeleteAsync(supervisorId, dto.CivilRecord);
                return Json(new { success = true, message = "تم حذف السجل بنجاح" });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = ex.Message });
            }
        }
    }
}