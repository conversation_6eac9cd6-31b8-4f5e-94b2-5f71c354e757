using Microsoft.AspNetCore.Mvc;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Web.ViewModels;
using Microsoft.AspNetCore.Mvc.Rendering;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;
using Microsoft.Extensions.Logging;
using System.IO.Compression;
using System.IO;
using System.Security.Claims;
using Microsoft.AspNetCore.Authorization;
using OrderFlowCore.Core.Entities;

namespace OrderFlowCore.Web.Controllers
{
    [Authorize]
    public class AssistantManagerController : Controller
    {
        private readonly IOrderService _orderService;
        private readonly ILogger<AssistantManagerController> _logger;

        public AssistantManagerController(IOrderService orderService, ILogger<AssistantManagerController> logger)
        {
            _orderService = orderService;
            _logger = logger;
        }

        [HttpGet]
        public async Task<IActionResult> Index()
        {
            var assistantManagerId = AssistantManagerType.A1;
            if (assistantManagerId == AssistantManagerType.Unknown)
            {
                TempData["ErrorMessage"] = "لم يتم تحديد مساعد المدير. يرجى تسجيل الدخول.";
                return RedirectToAction("AccessDenied", "Auth");
            }

            var viewModel = new AssistantManagerViewModel();

            // Get orders for assistant manager
            var ordersResult = await _orderService.GetAssistantManagerOrdersAsync(assistantManagerId);
            if (ordersResult.IsSuccess)
            {
                viewModel.OrderNumbers = ordersResult.Data.Select(o => new SelectListItem
                {
                    Value = o.Id.ToString(),
                    Text = $"{o.Id} - {o.EmployeeName} - {o.Department}"
                }).ToList();

            }
            else
            {
                TempData["ErrorMessage"] = ordersResult.Message;
            }

            return View(viewModel);
        }

        [HttpPost]
        public async Task<IActionResult> GetOrderDetails(int orderId)
        {
            var result = await _orderService.GetOrderDetailsAsync(orderId);
            if (!result.IsSuccess)
            {
                return Json(new { success = false, message = result.Message });
            }

            return Json(new { success = true, data = result.Data });
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ConfirmOrder(int orderId)
        {

            if (orderId <= 0)
            {
                TempData["ErrorMessage"] = "يرجى اختيار رقم الطلب.";
                return RedirectToAction(nameof(Index));
            }
            var userName = User.Identity?.Name;
            var result = await _orderService.ConfirmOrderByAssistantManagerAsync(orderId, userName);

            if (result.IsSuccess)
            {
                TempData["SuccessMessage"] = result.Message;
            }
            else
            {
                TempData["ErrorMessage"] = result.Message;
            }

            return RedirectToAction(nameof(Index));

        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ReturnToManager(int orderId, string returnReason)
        {

            if (orderId <= 0)
            {
                TempData["ErrorMessage"] = "يرجى اختيار رقم الطلب.";
                return RedirectToAction(nameof(Index));
            }

            if (string.IsNullOrWhiteSpace(returnReason))
            {
                TempData["ErrorMessage"] = "يرجى إدخال سبب الإعادة إلى مدير القسم.";
                return RedirectToAction(nameof(Index));
            }
            var userName = User.Identity?.Name;
            var result = await _orderService.ReturnOrderToDirectManagerAsync(orderId, returnReason, userName);
            if (result.IsSuccess)
            {
                TempData["SuccessMessage"] = result.Message;
            }
            else
            {
                TempData["ErrorMessage"] = result.Message;
            }

            return RedirectToAction(nameof(Index));

        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> RejectOrder(int orderId, string rejectReason)
        {

            if (orderId <= 0)
            {
                TempData["ErrorMessage"] = "يرجى اختيار رقم الطلب.";
                return RedirectToAction(nameof(Index));
            }

            if (string.IsNullOrWhiteSpace(rejectReason))
            {
                TempData["ErrorMessage"] = "يرجى إدخال سبب الإلغاء.";
                return RedirectToAction(nameof(Index));
            }

            var userName = User.Identity?.Name;
            var result = await _orderService.RejectOrderByAssistantManagerAsync(orderId, rejectReason, userName);

            if (result.IsSuccess)
            {
                TempData["SuccessMessage"] = result.Message;
            }
            else
            {
                TempData["ErrorMessage"] = result.Message;
            }

            return RedirectToAction(nameof(Index));

        }

        [HttpGet]
        public async Task<IActionResult> DownloadAttachments(int orderId)
        {
            try
            {
                var result = await _orderService.GetOrderDetailsAsync(orderId);
                if (!result.IsSuccess)
                {
                    return NotFound("لم يتم العثور على الطلب");
                }

                var order = result.Data;
                bool filesFound = false;

                using (MemoryStream zipStream = new MemoryStream())
                {
                    using (ZipArchive zipArchive = new ZipArchive(zipStream, ZipArchiveMode.Create, true))
                    {
                        // Process attachments (assuming they are stored as byte arrays)
                        // This would need to be implemented based on your file storage mechanism
                        var attachments = new[] { order.File1Url, order.File2Url, order.File3Url, order.File4Url };

                        for (int i = 0; i < attachments.Length; i++)
                        {
                            if (!string.IsNullOrEmpty(attachments[i]))
                            {
                                try
                                {
                                    // This is a placeholder - you'll need to implement actual file retrieval
                                    // based on your file storage mechanism
                                    string fileName = CleanFileName($"مرفق_{i + 1}_طلب_{orderId}_{order.EmployeeName}.pdf");

                                    // For now, we'll create an empty file - you'll need to implement actual file retrieval
                                    ZipArchiveEntry zipEntry = zipArchive.CreateEntry(fileName);
                                    using (Stream entryStream = zipEntry.Open())
                                    {
                                        // Placeholder - implement actual file content retrieval
                                        byte[] fileContent = Array.Empty<byte>(); // Get actual file content
                                        entryStream.Write(fileContent, 0, fileContent.Length);
                                    }

                                    filesFound = true;
                                }
                                catch (Exception ex)
                                {
                                    _logger.LogError(ex, $"Error processing attachment {i + 1}");
                                    continue;
                                }
                            }
                        }
                    }

                    if (filesFound)
                    {
                        string zipFileName = CleanFileName($"مرفقات_طلب_{orderId}_{order.EmployeeName}.zip");

                        return File(zipStream.ToArray(), "application/zip", zipFileName);
                    }
                    else
                    {
                        TempData["ErrorMessage"] = "لا توجد ملفات صالحة لتحميلها.";
                        return RedirectToAction(nameof(Index));
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error downloading attachments");
                TempData["ErrorMessage"] = "حدث خطأ أثناء تحميل الملفات";
                return RedirectToAction(nameof(Index));
            }
        }

        private string CleanFileName(string fileName)
        {
            char[] invalidChars = Path.GetInvalidFileNameChars();
            foreach (char c in invalidChars)
            {
                fileName = fileName.Replace(c, '_');
            }

            const int maxLength = 100;
            if (fileName.Length > maxLength)
            {
                string extension = Path.GetExtension(fileName);
                fileName = fileName.Substring(0, maxLength - extension.Length) + extension;
            }

            return fileName;
        }
    }
}