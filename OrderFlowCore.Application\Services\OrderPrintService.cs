using Microsoft.Extensions.Logging;
using OrderFlowCore.Application.Common;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Interfaces.Repositories;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Core.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace OrderFlowCore.Application.Services
{
    public interface IOrderPrintService
    {
        Task<ServiceResult<List<OrderPrintListItemDto>>> GetPrintableOrdersAsync(string searchTerm, string filter);
        Task<ServiceResult<OrderPrintDetailsDto>> GetOrderPrintDetailsAsync(int orderId);
        Task<ServiceResult<byte[]>> DownloadOrderAttachmentsZipAsync(int orderId);
        Task<ServiceResult<byte[]>> GenerateOrderPdfAsync(int orderId);
    }

    public class OrderPrintService : IOrderPrintService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IFileService _fileService;
        private readonly ILogger<OrderPrintService> _logger;

        public OrderPrintService(
            IUnitOfWork unitOfWork,
            IFileService fileService,
            ILogger<OrderPrintService> logger)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _fileService = fileService ?? throw new ArgumentNullException(nameof(fileService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public async Task<ServiceResult<List<OrderPrintListItemDto>>> GetPrintableOrdersAsync(string searchTerm, string filter)
        {
            try
            {
                _logger.LogInformation("Getting printable orders with search term: {SearchTerm}, filter: {Filter}", searchTerm, filter);

                var orders = await _unitOfWork.Orders.GetAllAsync();

                // Apply search filter
                if (!string.IsNullOrWhiteSpace(searchTerm))
                {
                    orders = orders.Where(o =>
                        o.EmployeeName.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                        o.EmployeeNumber.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                        o.CivilRecord.Contains(searchTerm, StringComparison.OrdinalIgnoreCase)
                    ).ToList();
                }

                // Apply date filter
                var today = DateTime.Today;
                switch (filter?.ToLower())
                {
                    case "today":
                        orders = orders.Where(o => o.CreatedAt.Date == today).ToList();
                        break;
                    case "week":
                        var weekStart = today.AddDays(-(int)today.DayOfWeek);
                        orders = orders.Where(o => o.CreatedAt.Date >= weekStart).ToList();
                        break;
                    case "month":
                        orders = orders.Where(o => o.CreatedAt.Month == today.Month && o.CreatedAt.Year == today.Year).ToList();
                        break;
                    case "all":
                    default:
                        // No additional filtering
                        break;
                }

                var result = orders.Select(o => new OrderPrintListItemDto
                {
                    OrderId = o.Id,
                    OrderNumber = o.Id.ToString(),
                    EmployeeName = o.EmployeeName
                }).ToList();

                return ServiceResult<List<OrderPrintListItemDto>>.Success(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting printable orders");
                return ServiceResult<List<OrderPrintListItemDto>>.Failure($"خطأ في جلب الطلبات: {ex.Message}");
            }
        }

        public async Task<ServiceResult<OrderPrintDetailsDto>> GetOrderPrintDetailsAsync(int orderId)
        {
            try
            {
                _logger.LogInformation("Getting order print details for order ID: {OrderId}", orderId);

                if (orderId <= 0)
                {
                    return ServiceResult<OrderPrintDetailsDto>.Failure("رقم الطلب غير صحيح");
                }

                var order = await _unitOfWork.Orders.GetByIdAsync(orderId);
                if (order == null)
                {
                    return ServiceResult<OrderPrintDetailsDto>.Failure("لم يتم العثور على الطلب");
                }

                var details = new OrderPrintDetailsDto
                {
                    OrderId = order.Id,
                    OrderNumber = order.Id.ToString(),
                    OrderDate = order.CreatedAt.ToString("yyyy-MM-dd"),
                    OrderStatus = order.OrderStatus.ToDisplayString(),
                    OrderType = order.OrderType,
                    EmployeeName = order.EmployeeName,
                    Department = order.Department,
                    Notes = order.Details ?? string.Empty,
                    JobTitle = order.JobTitle,
                    EmployeeNumber = order.EmployeeNumber,
                    CivilRecord = order.CivilRecord,
                    Nationality = order.Nationality,
                    MobileNumber = order.MobileNumber,
                    EmploymentType = order.EmploymentType,
                    Qualification = order.Qualification,
                    ManagerApproval = order.ConfirmedByDepartmentManager ?? "-",
                    SupervisorApproval = order.ConfirmedByAssistantManager ?? "-",
                    CoordinatorApproval = order.ConfirmedByCoordinator ?? "-",
                    CancellationReason = order.ReasonForCancellation ?? "-",
                    CoordinatorDetails = order.CoordinatorDetails ?? "-",
                    HRManagerApproval = order.HumanResourcesManager ?? "-"
                };

                return ServiceResult<OrderPrintDetailsDto>.Success(details);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting order print details for order ID: {OrderId}", orderId);
                return ServiceResult<OrderPrintDetailsDto>.Failure($"خطأ في جلب تفاصيل الطلب: {ex.Message}");
            }
        }

        public async Task<ServiceResult<byte[]>> DownloadOrderAttachmentsZipAsync(int orderId)
        {
            try
            {
                _logger.LogInformation("Downloading attachments for order ID: {OrderId}", orderId);

                if (orderId <= 0)
                {
                    return ServiceResult<byte[]>.Failure("رقم الطلب غير صحيح");
                }

                var order = await _unitOfWork.Orders.GetByIdAsync(orderId);
                if (order == null)
                {
                    return ServiceResult<byte[]>.Failure("لم يتم العثور على الطلب");
                }

                var fileUrls = new List<string>();
                if (!string.IsNullOrEmpty(order.File1Url)) fileUrls.Add(order.File1Url);
                if (!string.IsNullOrEmpty(order.File2Url)) fileUrls.Add(order.File2Url);
                if (!string.IsNullOrEmpty(order.File3Url)) fileUrls.Add(order.File3Url);
                if (!string.IsNullOrEmpty(order.File4Url)) fileUrls.Add(order.File4Url);

                if (!fileUrls.Any())
                {
                    return ServiceResult<byte[]>.Failure("لا توجد مرفقات لهذا الطلب");
                }

                // Use FileService to create ZIP
                var zipResult = await _fileService.DownloadFilesZipAsync(fileUrls);
                if (!zipResult.IsSuccess)
                {
                    return ServiceResult<byte[]>.Failure(zipResult.Message);
                }

                return ServiceResult<byte[]>.Success(zipResult.Data, "تم تحميل المرفقات بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error downloading attachments for order ID: {OrderId}", orderId);
                return ServiceResult<byte[]>.Failure($"خطأ في تحميل المرفقات: {ex.Message}");
            }
        }

        public async Task<ServiceResult<byte[]>> GenerateOrderPdfAsync(int orderId)
        {
            try
            {
                _logger.LogInformation("Generating PDF for order ID: {OrderId}", orderId);

                if (orderId <= 0)
                {
                    return ServiceResult<byte[]>.Failure("رقم الطلب غير صحيح");
                }

                var order = await _unitOfWork.Orders.GetByIdAsync(orderId);
                if (order == null)
                {
                    return ServiceResult<byte[]>.Failure("لم يتم العثور على الطلب");
                }

                // Use FileService to generate PDF
                var pdfResult = await _fileService.GenerateOrderPdfAsync(order);
                if (!pdfResult.IsSuccess)
                {
                    return ServiceResult<byte[]>.Failure(pdfResult.Message);
                }

                return ServiceResult<byte[]>.Success(pdfResult.Data, "تم إنشاء ملف PDF بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating PDF for order ID: {OrderId}", orderId);
                return ServiceResult<byte[]>.Failure($"خطأ في إنشاء ملف PDF: {ex.Message}");
            }
        }
    }
}
