using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Core.Entities;
using System;
using System.Collections.Generic;

namespace OrderFlowCore.Web.ViewModels
{
    public class OrderDetailsViewModel
    {
        public int Id { get; set; }
        public string OrderDate { get; set; }
        public string OrderType { get; set; }
        public string EmployeeName { get; set; }

        public string JobTitle { get; set; }
        public string EmployeeNumber { get; set; }
        public string CivilRecord { get; set; }

        public string Nationality { get; set; }
        public string MobileNumber { get; set; }
        public string EmploymentType { get; set; }
        public string Qualification { get; set; }
        public string Department { get; set; }
        public string Details { get; set; }
        public string OrderStatus { get; set; }
        public string ConfirmedByDepartmentManager { get; set; }
        public string ConfirmedByAssistantManager { get; set; }
        public string ConfirmedByCoordinator { get; set; }
        public string ReasonForCancellation { get; set; }
        public string CoordinatorDetails { get; set; }
        public string SupervisorOfEmployeeServices { get; set; }
        public string SupervisorOfHumanResourcesPlanning { get; set; }
        public string SupervisorOfInformationTechnology { get; set; }
        public string SupervisorOfAttendance { get; set; }
        public string SupervisorOfMedicalRecords { get; set; }
        public string SupervisorOfPayrollAndBenefits { get; set; }
        public string SupervisorOfLegalAndCompliance { get; set; }
        public string SupervisorOfHumanResourcesServices { get; set; }
        public string SupervisorOfHousing { get; set; }
        public string SupervisorOfFiles { get; set; }
        public string SupervisorOfOutpatientClinics { get; set; }
        public string SupervisorOfSocialSecurity { get; set; }
        public string SupervisorOfInventoryControl { get; set; }
        public string SupervisorOfRevenueDevelopment { get; set; }
        public string SupervisorOfSecurity { get; set; }
        public string SupervisorOfMedicalConsultation { get; set; }
        public string HumanResourcesManager { get; set; }
        public string TransferType { get; set; }
        public string SupervisorNotes { get; set; }

        public string File1Url { get; set; }

        public string File2Url { get; set; }

        public string File3Url { get; set; }

        public string File4Url { get; set; }

        // map from OrderDto to vm
        public static OrderDetailsViewModel FromDto(OrderDetailsDto orderDto)
        {
            return new OrderDetailsViewModel
            {
                Id = orderDto.Id,
                OrderDate = orderDto.OrderDate,
                OrderType = orderDto.OrderType,
                EmployeeName = orderDto.EmployeeName,
                JobTitle = orderDto.JobTitle,
                EmployeeNumber = orderDto.EmployeeNumber,
                CivilRecord = orderDto.CivilRecord,
                Nationality = orderDto.Nationality,
                MobileNumber = orderDto.MobileNumber,
                EmploymentType = orderDto.EmploymentType,
                Qualification = orderDto.Qualification,
                Department = orderDto.Department,
                Details = orderDto.Details,
                OrderStatus = orderDto.OrderStatus,
                ConfirmedByDepartmentManager = orderDto.ConfirmedByDepartmentManager,
                ConfirmedByAssistantManager = orderDto.ConfirmedByAssistantManager,
                ConfirmedByCoordinator = orderDto.ConfirmedByCoordinator,
                ReasonForCancellation = orderDto.ReasonForCancellation,
                CoordinatorDetails = orderDto.CoordinatorDetails,
                SupervisorOfEmployeeServices = orderDto.SupervisorOfEmployeeServices,
                SupervisorOfHumanResourcesPlanning = orderDto.SupervisorOfHumanResourcesPlanning,
                SupervisorOfInformationTechnology = orderDto.SupervisorOfInformationTechnology,
                SupervisorOfAttendance = orderDto.SupervisorOfAttendance,
                SupervisorOfMedicalRecords = orderDto.SupervisorOfMedicalRecords,
                SupervisorOfPayrollAndBenefits = orderDto.SupervisorOfPayrollAndBenefits,
                SupervisorOfLegalAndCompliance = orderDto.SupervisorOfLegalAndCompliance,
                SupervisorOfHumanResourcesServices = orderDto.SupervisorOfHumanResourcesServices,
                SupervisorOfHousing = orderDto.SupervisorOfHousing,
                SupervisorOfFiles = orderDto.SupervisorOfFiles,
                SupervisorOfOutpatientClinics = orderDto.SupervisorOfOutpatientClinics,
                SupervisorOfSocialSecurity = orderDto.SupervisorOfSocialSecurity,
                SupervisorOfInventoryControl = orderDto.SupervisorOfInventoryControl,
                SupervisorOfRevenueDevelopment = orderDto.SupervisorOfRevenueDevelopment,
                SupervisorOfSecurity = orderDto.SupervisorOfSecurity,
                SupervisorOfMedicalConsultation = orderDto.SupervisorOfMedicalConsultation,
                HumanResourcesManager = orderDto.HumanResourcesManager,
                TransferType = orderDto.TransferType,
                SupervisorNotes = orderDto.SupervisorNotes,
                File1Url = orderDto.File1Url,
                File2Url = orderDto.File2Url,
                File3Url = orderDto.File3Url,
                File4Url = orderDto.File4Url,
            };
        }
        public static OrderDetailsDto ToDto(OrderDetailsViewModel model)
        {
            return new OrderDetailsDto
            {
                Id = model.Id,
                OrderDate = model.OrderDate,
                OrderType = model.OrderType,
                EmployeeName = model.EmployeeName,
                JobTitle = model.JobTitle,
                EmployeeNumber = model.EmployeeNumber,
                CivilRecord = model.CivilRecord,
                Nationality = model.Nationality,
                MobileNumber = model.MobileNumber,
                EmploymentType = model.EmploymentType,
                Qualification = model.Qualification,
                Department = model.Department,
                Details = model.Details,
                OrderStatus = model.OrderStatus,
                ConfirmedByDepartmentManager = model.ConfirmedByDepartmentManager,
                ConfirmedByAssistantManager = model.ConfirmedByAssistantManager,
                ConfirmedByCoordinator = model.ConfirmedByCoordinator,
                ReasonForCancellation = model.ReasonForCancellation,
                CoordinatorDetails = model.CoordinatorDetails,
                SupervisorOfEmployeeServices = model.SupervisorOfEmployeeServices,
                SupervisorOfHumanResourcesPlanning = model.SupervisorOfHumanResourcesPlanning,
                SupervisorOfInformationTechnology = model.SupervisorOfInformationTechnology,
                SupervisorOfAttendance = model.SupervisorOfAttendance,
                SupervisorOfMedicalRecords = model.SupervisorOfMedicalRecords,
                SupervisorOfPayrollAndBenefits = model.SupervisorOfPayrollAndBenefits,
                SupervisorOfLegalAndCompliance = model.SupervisorOfLegalAndCompliance,
                SupervisorOfHumanResourcesServices = model.SupervisorOfHumanResourcesServices,
                SupervisorOfHousing = model.SupervisorOfHousing,
                SupervisorOfFiles = model.SupervisorOfFiles,
                SupervisorOfOutpatientClinics = model.SupervisorOfOutpatientClinics,
                SupervisorOfSocialSecurity = model.SupervisorOfSocialSecurity,
                SupervisorOfInventoryControl = model.SupervisorOfInventoryControl,
                SupervisorOfRevenueDevelopment = model.SupervisorOfRevenueDevelopment,
                SupervisorOfSecurity = model.SupervisorOfSecurity,
                SupervisorOfMedicalConsultation = model.SupervisorOfMedicalConsultation,
                HumanResourcesManager = model.HumanResourcesManager,
                TransferType = model.TransferType,
                SupervisorNotes = model.SupervisorNotes,
                File1Url = model.File1Url,
                File2Url = model.File2Url,
                File3Url = model.File3Url,
                File4Url = model.File4Url,
            };
        }
    }
} 