@model OrderFlowCore.Web.ViewModels.OrderPrintViewModel
@{
    ViewBag.Title = "نظام طباعة الطلبات المعتمدة";
}

<!-- Add your CSS here or in a separate file -->

<div class="card border-success shadow-lg mt-5">
    <div class="card-header bg-success text-white py-3 position-sticky-top">
        <h4 class="card-title mb-0 fw-bold d-flex align-items-center">
            <i class="fas fa-print me-3 fa-beat-fade"></i>
            نظام طباعة الطلبات المعتمدة
        </h4>
    </div>
    <div class="card-body p-4">
        @using (Html.BeginForm("Index", "OrderPrint", FormMethod.Post, new { @class = "row g-3 mb-4" }))
        {
            <div class="col-12 col-lg-8">
                <div class="input-group input-group-lg has-validation">
                    @Html.TextBoxFor(m => m.SearchTerm, new { @class = "form-control border-2 border-success rounded-start-4 shadow-sm", placeholder = "ابحث برقم الطلب / اسم الموظف..." })
                    <button class="input-group-text bg-success text-white rounded-end-4 shadow-sm" type="submit">
                        <i class="fas fa-search fa-fw"></i>
                    </button>
                </div>
            </div>
            <div class="col-12 col-lg-4">
                <div class="d-grid gap-2 d-lg-flex align-items-stretch">
                    <button name="filter" value="today" class="btn btn-outline-success">اليوم</button>
                    <button name="filter" value="week" class="btn btn-outline-success">أسبوع</button>
                    <button name="filter" value="month" class="btn btn-outline-success">شهر</button>
                    <button name="filter" value="all" class="btn btn-outline-success">الكل</button>
                </div>
            </div>
            <div class="col-12">
                @Html.DropDownList("SelectedOrder.OrderNumber", Model.Orders, "-- اختر الطلب للطباعة --", new { @class = "form-select form-select-lg border-2 border-success rounded-3 shadow-sm", id = "ddlPrintOrders" })
            </div>
        }
    </div>
</div>

<div class="button-container" style="text-align: center; margin-top: 20px;">
    <form method="post" action="@Url.Action("PrintOrder", "OrderPrint")">
        <input type="hidden" name="orderId" id="selectedOrderId" value="@Model.SelectedOrder?.OrderNumber" />
        <button type="submit" class="submit-button">طباعة</button>
    </form>
    <form method="post" action="@Url.Action("DownloadAttachments", "OrderPrint")">
        <input type="hidden" name="orderId" id="selectedOrderId2" value="@Model.SelectedOrder?.OrderNumber" />
        <button type="submit" class="submit-button">تحميل مرفقات الطلب</button>
    </form>
</div>

@if (!string.IsNullOrEmpty(Model.Message))
{
    <div style="text-align: center; margin-top: 10px;">
        <span class="text-success">@Model.Message</span>
    </div>
}
@if (!string.IsNullOrEmpty(Model.Error))
{
    <div style="text-align: center; margin-top: 10px;">
        <span class="text-danger">@Model.Error</span>
    </div>
}

<div id="orderDetailsPanel">
@if (Model.SelectedOrder != null)
{
    @Html.Partial("_OrderDetailsPanel", Model.SelectedOrder)
}
</div>

@section Scripts {
    <script>
        $(function () {
            $('#ddlPrintOrders').change(function () {
                var orderId = $(this).val();
                if (orderId) {
                    $.get('@Url.Action("GetOrderDetails", "OrderPrint")', { orderId: orderId }, function (html) {
                        $('#orderDetailsPanel').html(html);
                    });
                    $('#selectedOrderId').val(orderId);
                    $('#selectedOrderId2').val(orderId);
                }
            });
        });
    </script>
} 