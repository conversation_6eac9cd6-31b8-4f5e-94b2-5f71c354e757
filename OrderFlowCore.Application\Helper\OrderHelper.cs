﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace OrderFlowCore.Application.Helper
{
    public static class OrderHelper
    {
        public static string ConfirmedBy(string? username) => $"{DateTime.Now:yyyy-MM-dd} | اعتماد بواسطة: {username}";

        public static string RejectedBy(string? username) => $"{DateTime.Now:yyyy-MM-dd} | تم الالغاء بواسطة: {username}";

        public static string RestoredBy(string? username) => $"{DateTime.Now:yyyy-MM-dd} | استعادة بواسطة: {username}";

        public static string ReturnedBy(string? username) => $"{DateTime.Now:yyyy-MM-dd} | تمت الإعادة بواسطة: {username}";

        public static string OrderUnderImplementation() => $"{DateTime.Now:yyyy-MM-dd} | الطلب قيد التنفيذ";

        public static string OrderNeedActionByCoordinator(string username)
            => $"{DateTime.Now:yyyy-MM-dd} | طلب إجراء من المنسق: {username}";

        public static string OrderDirectToManager(string username)
            => $"{DateTime.Now:yyyy-MM-dd} | تم التحويل مباشر للمدير: {username}";


    }

    public static class OrderTransferTypes
    {
        public const string Manually = "يدوي";
        public const string Auto = "تلقائي";
        public const string Direct = "سريع";
        public const string DirectToManager = "مباشر للمدير";
    }
}
