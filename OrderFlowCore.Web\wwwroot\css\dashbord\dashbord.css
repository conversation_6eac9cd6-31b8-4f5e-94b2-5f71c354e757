﻿.btn-outline-danger {
    border: 1px solid #dc3545; /* Red border */
    color: #dc3545; /* Red text */
    background-color: transparent; /* Transparent background */
    transition: all 0.3s ease; /* Smooth transition for hover effects */
    padding: 6px 12px; /* Padding for better spacing */
    border-radius: 4px; /* Rounded corners */
    font-size: 0.9rem; /* Slightly smaller font size */
}

    .btn-outline-danger:hover {
        background-color: #dc3545; /* Red background on hover */
        color: #fff; /* White text on hover */
        border-color: #dc3545; /* Red border on hover */
    }

    .btn-outline-danger i {
        margin-right: 5px; /* Space between icon and text */
    }
/* Set Arabic font family */
body {
    font-family: 'Cairo', sans-serif;
    direction: rtl;
    background-color: #f8f9fa; /* Light background for better contrast */
}

/* Sidebar Styles */
.app-sidebar {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    width: 250px;
    z-index: 1030;
    overflow-y: auto; /* Make sidebar scrollable */
    background-color: #343a40;
    box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
    transition: right 0.3s ease;
}

.sidebar-brand {
    padding: 20px;
    background-color: #2c3136;
    border-bottom: 1px solid #4b545c;
    display: flex;
    align-items: center; /* Vertically center the content */
    justify-content: center; /* Horizontally center the content */
}

.brand-link {
    color: #fff !important;
    font-size: 1.2rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.sidebar-brand .brand-link i {
    font-size: 1.5rem;
}

body.sidebar-collapse .app-sidebar {
    right: -250px;
}

body.sidebar-collapse .content-wrapper,
body.sidebar-collapse .main-header {
    margin-right: 0;
}

.pushmenu-btn {
    z-index: 1032;
    position: relative;
}

/* Sidebar Menu Styles */
.nav-sidebar {
    padding: 10px 0;
}



.nav-sidebar .nav-link {
    padding: 12px 20px;
    color: #c2c7d0;
    transition: background-color 0.3s ease, color 0.3s ease;
    border-radius: 4px;
    margin: 4px 10px;
    display: flex;
    align-items: center;
    gap: 10px;
}

    .nav-sidebar .nav-link:hover {
        background-color: #4b545c;
        color: #fff;
    }

    .nav-sidebar .nav-link.active {
        background-color: #007bff;
        color: #fff;
    }

.nav-sidebar .nav-icon {
    font-size: 1.1rem;
    width: 20px;
    text-align: center;
}

/* User Panel Styles */
.user-panel {
    padding: 15px;
    border-bottom: 1px solid #4b545c;
    background-color: #2c3136;
}

    .user-panel .info {
        color: #fff;
    }

        .user-panel .info small {
            color: #c2c7d0;
            font-size: 0.9rem;
        }

/* Navbar Styles */
.main-header {
    background-color: #fff;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    z-index: 1031;
    position: relative;
    transition: margin-right 0.3s ease;
}

.navbar-nav .nav-link {
    color: #343a40;
    padding: 10px 15px;
    transition: color 0.3s ease;
    position: relative;
}

    .navbar-nav .nav-link:hover {
        color: #007bff;
    }

/* Notification Badge */
.icon-badge {
    position: absolute;
    top: 5px;
    left: 5px;
    background-color: #dc3545;
    color: white;
    border-radius: 50%;
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    min-width: 1.5rem;
    text-align: center;
    font-weight: bold;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
}

/* Notification Dropdown Styles */
.notification-dropdown {
    width: 350px;
    max-height: 500px;
    overflow-y: auto;
    border: 1px solid #ddd;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    padding: 0;
}

.notification-item {
    padding: 12px;
    border-bottom: 1px solid #eee;
    transition: background-color 0.3s ease;
    position: relative;
}

    .notification-item:hover {
        background-color: #f8f9fa;
    }

    .notification-item.unread {
        border-right: 3px solid #007bff;
        background-color: #f0f7ff;
    }

    .notification-item h6 {
        font-weight: 600;
        margin-bottom: 5px;
        padding-left: 60px; /* Make room for buttons */
    }

    .notification-item p {
        margin-bottom: 5px;
        white-space: normal;
        word-wrap: break-word;
        overflow-wrap: break-word;
        padding-left: 60px; /* Make room for buttons */
    }

    .notification-item small {
        color: #6c757d;
        display: block;
        padding-left: 60px; /* Make room for buttons */
    }

    .notification-item .notification-actions {
        position: absolute;
        left: 10px;
        top: 50%;
        transform: translateY(-50%);
        display: flex;
        flex-direction: column;
        gap: 5px;
    }

    .notification-item .btn {
        padding: 4px 8px;
        font-size: 0.8rem;
    }

.dropdown-header {
    background-color: #f8f9fa;
    font-weight: 600;
    padding: 10px 15px;
    border-bottom: 1px solid #ddd;
}

/* Mobile optimization */
@media (max-width: 576px) {
    .notification-dropdown {
        width: 300px;
    }

    .notification-item h6 {
        font-size: 0.9rem;
    }

    .notification-item p {
        font-size: 0.8rem;
    }
}

/* Content Wrapper Styles */
.content-wrapper {
    margin-right: 250px;
    margin-left: 0;
    padding: 20px;
    background-color: #fff;
    transition: margin-right 0.3s ease;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

/* Dark Mode Styles */
.dark-mode .main-header {
    background-color: #343a40 !important;
    border-color: #454d55 !important;
}

.dark-mode .navbar-nav .nav-link {
    color: #fff !important;
}

.dark-mode .content-wrapper {
    background-color: #454d55 !important;
    color: #fff !important;
}

.dark-mode .dropdown-menu {
    background-color: #343a40 !important;
    border-color: #454d55 !important;
}

.dark-mode .dropdown-item {
    color: #fff !important;
}

    .dark-mode .dropdown-item:hover {
        background-color: #454d55 !important;
    }

/* Dashboard Specific Styles */
.welcome-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem;
    border-radius: 15px;
    margin-bottom: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.welcome-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.welcome-header h2 {
    font-weight: 700;
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.welcome-header p {
    margin-bottom: 0.25rem;
    opacity: 0.9;
}

.welcome-header i {
    opacity: 0.8;
}

/* Statistics Cards */
.stat-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    border: none;
    position: relative;
    overflow: hidden;
    transform: translateY(0);
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--card-color), var(--card-color-light));
}

.stat-card.primary {
    --card-color: #007bff;
    --card-color-light: #4da3ff;
}

.stat-card.warning {
    --card-color: #ffc107;
    --card-color-light: #ffd54f;
}

.stat-card.success {
    --card-color: #28a745;
    --card-color-light: #66bb6a;
}

.stat-card.danger {
    --card-color: #dc3545;
    --card-color-light: #ef5350;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.stat-card.animate-in {
    animation: slideInUp 0.6s ease-out;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--card-color);
    line-height: 1;
    margin-bottom: 0.5rem;
}

.stat-icon {
    font-size: 3rem;
    opacity: 0.2;
    color: var(--card-color);
}

/* Dashboard Cards */
.dashboard-card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    background: white;
    overflow: hidden;
}

.dashboard-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.12);
}

.dashboard-card .card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
    padding: 1.25rem 1.5rem;
    font-weight: 600;
    color: #495057;
}

.dashboard-card .card-body {
    padding: 1.5rem;
}

/* Chart Container */
.chart-container {
    position: relative;
    height: 300px;
    margin: 1rem 0;
}

/* Progress Bars */
.progress-modern {
    height: 8px;
    background-color: #e9ecef;
    border-radius: 10px;
    overflow: hidden;
    margin: 0.5rem 0;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.progress-modern .progress-bar {
    height: 100%;
    border-radius: 10px;
    background: linear-gradient(90deg, var(--progress-color), var(--progress-color-light));
    transition: width 1.5s ease-out;
    position: relative;
}

.progress-modern .progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 2s infinite;
}

/* Workflow Stages */
.workflow-stage {
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 10px;
    border-left: 4px solid var(--stage-color, #007bff);
    transition: all 0.3s ease;
    opacity: 0;
    transform: translateX(20px);
}

.workflow-stage.fade-in {
    opacity: 1;
    transform: translateX(0);
}

.workflow-stage:hover {
    background: #e9ecef;
    transform: translateX(5px);
}

/* Department Distribution */
.department-item {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 10px;
    padding: 1rem;
    text-align: center;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.department-item:hover {
    border-color: #007bff;
    transform: scale(1.05);
    box-shadow: 0 5px 15px rgba(0, 123, 255, 0.2);
}

/* Animations */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes shimmer {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

/* Error Alert Styling */
.error-alert {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
    border: none;
    border-radius: 10px;
    color: white;
    padding: 1rem 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 5px 15px rgba(255, 107, 107, 0.3);
    position: relative;
    overflow: hidden;
}

.error-alert::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    animation: shimmer 3s infinite;
}

/* Responsive Design */
@media (max-width: 768px) {
    .welcome-header {
        padding: 1.5rem;
        text-align: center;
    }
    
    .welcome-header .col-md-2 {
        margin-bottom: 1rem;
    }
    
    .stat-card {
        margin-bottom: 1rem;
    }
    
    .stat-number {
        font-size: 2rem;
    }
    
    .stat-icon {
        font-size: 2.5rem;
    }
    
    .chart-container {
        height: 250px;
    }
    
    .workflow-stage {
        margin-bottom: 1rem;
    }
}

@media (max-width: 576px) {
    .welcome-header {
        padding: 1rem;
    }
    
    .stat-card {
        padding: 1rem;
    }
    
    .dashboard-card .card-body {
        padding: 1rem;
    }
    
    .chart-container {
        height: 200px;
    }
}

/* Dark Mode Support */
.dark-mode .dashboard-card {
    background: #2d3748;
    color: #e2e8f0;
}

.dark-mode .dashboard-card .card-header {
    background: linear-gradient(135deg, #4a5568 0%, #2d3748 100%);
    border-bottom-color: #4a5568;
    color: #e2e8f0;
}

.dark-mode .stat-card {
    background: #2d3748;
    color: #e2e8f0;
}

.dark-mode .workflow-stage {
    background: #4a5568;
    color: #e2e8f0;
}

.dark-mode .workflow-stage:hover {
    background: #2d3748;
}

.dark-mode .department-item {
    background: linear-gradient(135deg, #4a5568 0%, #2d3748 100%);
    color: #e2e8f0;
}

.dark-mode .progress-modern {
    background-color: #4a5568;
}

/* Loading States */
.loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* Custom Scrollbar for Dashboard */
.dashboard-container::-webkit-scrollbar {
    width: 8px;
}

.dashboard-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

.dashboard-container::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 10px;
}

.dashboard-container::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* ===== DirectManager and AssistantManager Styles ===== */

/* Button Styles */
.submit-button {
    border-style: none;
    border-color: inherit;
    border-width: medium;
    padding: 12px 24px;
    background-color: #28a745;
    color: white;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    margin: 10px 5px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.submit-button:hover {
    background-color: #218838;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.reject-button {
    border-style: none;
    border-color: inherit;
    border-width: medium;
    padding: 12px 24px;
    background-color: #dc3545;
    color: white;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    margin: 10px 5px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.reject-button:hover {
    background-color: #c82333;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.download-button {
    border-style: none;
    border-color: inherit;
    border-width: medium;
    padding: 12px 24px;
    background-color: #17a2b8;
    color: white;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    margin: 10px 5px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.download-button:hover {
    background-color: #138496;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

/* Action Card Styles */
.action-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    margin: 15px 0;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-left: 4px solid #007bff;
    transition: all 0.3s ease;
}

.action-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.action-buttons-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    align-items: center;
    margin-top: 15px;
}

/* Input Styles */
.reason-input {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 12px 16px;
    font-size: 16px;
    transition: border-color 0.3s ease;
    margin: 10px 5px;
    width: 300px;
}

.reason-input:focus {
    border-color: #007bff;
    outline: none;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

/* Order Selection Styles */
.order-select-container {
    background-color: #0891b2;
    border-radius: 12px;
    padding: 25px;
    color: white;
    margin-bottom: 20px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.order-select-container label {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 10px;
    display: block;
}

.form-select {
    border: none;
    border-radius: 8px;
    padding: 12px 16px;
    font-size: 18px;
    background-color: white;
    color: #333;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.form-select:focus {
    box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.3);
    outline: none;
}

/* Status Badge Styles */
.status-badge {
    display: inline-block;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 600;
    text-align: center;
    min-width: 100px;
}

.status-pending {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.status-approved {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-rejected {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Quick Actions Styles */
.quick-actions {
    background: white;
    border-radius: 12px;
    padding: 20px;
    margin: 20px 0;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-top: 4px solid #28a745;
}

.quick-actions h4 {
    color: #28a745;
    margin-bottom: 15px;
    font-weight: 600;
}

.action-icon {
    margin-right: 8px;
    font-size: 18px;
}

/* Table Styles */
.details-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    margin-bottom: 20px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-radius: 12px;
    overflow: hidden;
    border: 2px solid #007bff;
}

.details-table th,
.details-table td {
    padding: 16px;
    text-align: center;
    border: 1px solid #ddd;
}

.details-table th {
    background-color: #f2f2f2;
    color: #333;
    font-weight: bold;
    font-size: 16px;
    border-right: 1px solid #ddd;
}

.details-table td {
    font-size: 16px;
    color: #333;
    background-color: #ffffff;
}

.details-table tr:nth-child(even) td {
    background-color: #ffffff;
}

.details-table tr:hover td {
    background-color: #e9e9e9;
}

.details-table th:first-child,
.details-table td:first-child {
    border-left: none;
}

.details-table th:last-child,
.details-table td:last-child {
    border-right: none;
}

.details-table tr:first-child th:first-child {
    border-top-left-radius: 12px;
}

.details-table tr:first-child th:last-child {
    border-top-right-radius: 12px;
}

.details-table tr:last-child td:first-child {
    border-bottom-left-radius: 12px;
}

.details-table tr:last-child td:last-child {
    border-bottom-right-radius: 12px;
}

.full-table-container {
    border: 3px solid #007bff;
    padding: 20px;
    border-radius: 15px;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
    margin-top: 20px;
}

/* Loading and Display Styles */
.loading {
    display: none;
    text-align: center;
    padding: 20px;
}

.order-details {
    display: none;
}

/* Alert Styles */
.alert {
    border-radius: 8px;
    padding: 15px 20px;
    margin: 15px 0;
    border: none;
    font-weight: 500;
}

.alert-success {
    background-color: #d4edda;
    color: #155724;
    border-left: 4px solid #28a745;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
    border-left: 4px solid #dc3545;
}

/* Animation Styles */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Order Selection Styles for AssistantManager */
.order-selection {
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

/* Responsive Styles */
@media (max-width: 768px) {
    .details-table th,
    .details-table td {
        font-size: 12px;
        padding: 8px;
    }
    
    .action-buttons-container {
        flex-direction: column;
        align-items: stretch;
    }
    
    .reason-input {
        width: 100%;
    }
}

/* ===== User Views Styles ===== */

/* Gradient Backgrounds */
.bg-gradient-info {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
}

.bg-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.bg-gradient-warning {
    background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%);
}

/* Profile Styles */
.profile-avatar {
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.profile-info-card {
    background: #f8f9fa;
    border-radius: 1rem;
    padding: 1.5rem;
    height: 100%;
}

.info-item {
    margin-bottom: 1.5rem;
}

.info-item:last-child {
    margin-bottom: 0;
}

.info-label {
    font-weight: bold;
    color: #495057;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.info-value {
    font-size: 1.1rem;
    color: #212529;
    padding: 0.5rem 0;
    border-bottom: 1px solid #dee2e6;
}

.info-value:last-child {
    border-bottom: none;
}

/* Form Styles */
.form-control-lg {
    padding: 0.75rem 1rem;
    font-size: 1.1rem;
}

.form-label {
    color: #495057;
    margin-bottom: 0.5rem;
}

/* Card Styles */
.card {
    border-radius: 1rem;
    overflow: hidden;
}

/* Button Styles */
.btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: 1.1rem;
    border-radius: 0.5rem;
    min-width: 200px;
}

/* Alert Styles */
.alert {
    border-radius: 0.5rem;
    border: none;
}

/* Badge Styles */
.badge {
    font-size: 0.9rem;
    padding: 0.5rem 1rem;
}

/* Password Strength Styles */
.password-strength .progress-bar {
    transition: all 0.3s ease;
}
/* Toggle Quick Actions Button Styles */
#toggleQuickActions { transition: all 0.3s ease; border-radius: 25px; padding: 10px 20px; font-weight: 500; box-shadow: 0 2px 8px rgba(0, 123, 255, 0.2); border: 2px solid #007bff; background: linear-gradient(135deg, #007bff, #0056b3); color: white; }

/* ===== HR Coordinator Specific Styles ===== */

/* Supervisors Container - New style not in existing CSS */
.supervisors-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
    margin: 15px 0;
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

/* Supervisors Section - New style not in existing CSS */
.supervisors-section {
    background-color: #fff;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 20px;
    border: 1px solid #dee2e6;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

/* Form Inputs Section - New style not in existing CSS */
.form-inputs-section {
    background-color: #fff;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 20px;
    border: 1px solid #dee2e6;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

/* Restore Section - New style not in existing CSS */
.restore-section {
    border: 2px solid #17a2b8;
    border-radius: 10px;
    padding: 20px;
    background-color: #f8f9fa;
    margin-top: 20px;
}

/* HR Coordinator Responsive Adjustments */
@media (max-width: 768px) {
    .supervisors-container {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 10px;
    }

    .supervisors-section,
    .form-inputs-section {
        padding: 15px;
    }

    .restore-section {
        padding: 15px;
    }
}

/* --- PathManagement/Index.cshtml styles moved here --- */
:root {
    --primary-color: #0066cc;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-bg: #f8f9fa;
    --card-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

/* Enhanced Navigation Tabs */
.nav-tabs-custom {
    background: white;
    padding: 1rem;
    border-radius: 12px;
    box-shadow: var(--card-shadow);
    margin-bottom: 2rem;
}
.nav-tabs-custom .nav-link {
    color: #6c757d;
    border: 2px solid transparent;
    border-radius: 8px;
    padding: 0.75rem 1.5rem;
    margin: 0 0.25rem;
    transition: all 0.3s ease;
    font-weight: 500;
}
.nav-tabs-custom .nav-link:hover {
    background-color: var(--light-bg);
    border-color: var(--light-bg);
}
.nav-tabs-custom .nav-link.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* Quick Actions Bar (unique gradient) */
.quick-actions {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1.5rem;
    border-radius: 12px;
    margin-bottom: 2rem;
}
.quick-action-btn {
    background: rgba(255,255,255,0.2);
    border: 1px solid rgba(255,255,255,0.3);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}
.quick-action-btn:hover {
    background: rgba(255,255,255,0.3);
    transform: translateY(-2px);
    color: white;
}

/* Enhanced Cards */
.card-custom {
    border: none;
    border-radius: 12px;
    box-shadow: var(--card-shadow);
    transition: all 0.3s ease;
    overflow: hidden;
}
.card-custom:hover {
    transform: translateY(-4px);
    box-shadow: 0 4px 16px rgba(0,0,0,0.15);
}
.card-header-custom {
    background: linear-gradient(135deg, var(--primary-color), #0052a3);
    color: white;
    padding: 1rem 1.5rem;
    border: none;
}

/* Form Enhancements */
.form-section {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: var(--card-shadow);
    margin-bottom: 2rem;
}
.form-control, .form-select {
    border-radius: 8px;
    border: 1px solid #e0e0e0;
    padding: 0.75rem;
    transition: all 0.3s ease;
}
.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0,102,204,0.25);
}

/* Supervisor Selection Grid */
.supervisor-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;
    padding: 1rem;
    background: var(--light-bg);
    border-radius: 8px;
}
.supervisor-card {
    background: white;
    padding: 1rem;
    border-radius: 8px;
    border: 2px solid transparent;
    transition: all 0.3s ease;
    cursor: pointer;
}
.supervisor-card:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
}
.supervisor-card.selected {
    background-color: #e7f3ff;
    border-color: var(--primary-color);
}

.supervisor-card label{
    cursor: pointer;
}
.supervisor-card input {
    cursor: pointer;
}

/* Action Buttons */
.btn-action {
    border-radius: 8px;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
    border: none;
}
.btn-primary-custom {
    background: linear-gradient(135deg, var(--primary-color), #0052a3);
    color: white;
}
.btn-primary-custom:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,102,204,0.4);
}

/* Status Badges (add .status-active, .status-inactive if not present) */
.status-badge {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
}
.status-active {
    background-color: #d4edda;
    color: #155724;
}
.status-inactive {
    background-color: #f8d7da;
    color: #721c24;
}

/* Table Enhancements */
.table-custom {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: var(--card-shadow);
}
.table-custom thead {
    background-color: var(--light-bg);
}
.table-custom th {
    border-bottom: 2px solid #dee2e6;
    padding: 1rem;
    font-weight: 600;
    color: #495057;
}
.table-custom td {
    padding: 1rem;
    vertical-align: middle;
}
.table-custom tbody tr:hover {
    background-color: #f8f9fa;
}

/* Floating Action Button */
.fab-container {
    position: fixed;
    bottom: 2rem;
    left: 2rem;
    z-index: 1000;
}
.fab {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color), #0052a3);
    color: white;
    border: none;
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    transition: all 0.3s ease;
}
.fab:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(0,0,0,0.4);
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: #6c757d;
}
.empty-state i {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

/* Loading Spinner */
.spinner-custom {
    display: inline-block;
    width: 1rem;
    height: 1rem;
    border: 2px solid #f3f3f3;
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .supervisor-grid {
        grid-template-columns: 1fr;
    }
    .quick-actions {
        text-align: center;
    }
    .quick-action-btn {
        display: block;
        margin: 0.5rem 0;
    }
}
/* --- End PathManagement/Index.cshtml styles --- */
