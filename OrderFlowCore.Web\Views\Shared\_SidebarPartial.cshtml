@using OrderFlowCore.Web.Models
@using System.Security.Claims

@model string
@{
    var menuItems = new List<MenuItem>
    {
        new MenuItem { Controller = "Dashboard", Icon = "fas fa-tachometer-alt", Title = "لوحه التحكم" },
        new MenuItem { Controller = "DirectManager", Icon = "fas fa-user-tie", Title = "مدير القــســـم - إدارة الطلبات" },
        new MenuItem { Controller = "AssistantManager", Icon = "fas fa-clipboard-check", Title = "مســاعد المدير - إدارة الطلبات" },
        new MenuItem { Controller = "HRCoordinator", Icon = "fas fa-user-tie", Title = "منسق الموارد البشرية - إدارة الطلبات" },
        new MenuItem { Controller = "SupervisorOrders", Icon = "fas fa-user-tie", Title = "الــمـــشرفــون - إدارة الطلبات" },
        new MenuItem { Controller = "OrderManager", Icon = "fas fa-user-tie", Title = "مدير الموارد البشرية - إدارة الطلبات" }
    };



    var settingsItems = new List<MenuItem>
    {
        new MenuItem { Controller = "Department", Icon = "fas fa-building", Title = "الأقسام" },
        new MenuItem { Controller = "Nationality", Icon = "fas fa-flag", Title = "الجنسيات" },
        new MenuItem { Controller = "Qualification", Icon = "fas fa-certificate", Title = "المؤهلات" },
        new MenuItem { Controller = "EmploymentType", Icon = "fas fa-briefcase", Title = "أنواع التوظيف" },
        new MenuItem { Controller = "JobType", Icon = "fas fa-user-tie", Title = "أنواع الوظائف" },
        new MenuItem { Controller = "OrdersType", Icon = "fas fa-user-tie", Title = "أنواع الطلبات" },
        new MenuItem { Controller = "Employee", Icon = "fas fa-user-tie", Title = "إدارة بيانات الموظفين" },
        new MenuItem { Controller = "Distribution", Icon = "fas fa-sitemap", Title = "إدارة التوزيع" },
        new MenuItem { Controller = "Accounts", Icon = "fas fa-users", Title = "إدارة الحسابات" },
        new MenuItem { Controller = "PathManagement", Icon = "fas fa-map-signs", Title = "إدارة المسارات" },
    };
}

<aside class="app-sidebar bg-dark shadow" data-bs-theme="dark">
    @* Brand Logo *@
    <div class="sidebar-brand">
        <a href="@Url.Action("Index", "Dashboard")" class="brand-link">
            <div class="text-center">
                <img width="50" src="~/img/star.png" alt="Logo" />
                <div>مستشفى بريدة المركزي</div>
            </div>
        </a>
    </div>

    @* User Panel *@
    <div class="user-panel d-flex align-items-center border-bottom border-secondary p-3">
        <div class="image me-3">
            <i class="fas fa-user-circle fa-2x text-light"></i>
        </div>
        <div class="info">
            <a class="d-block text-light fw-bold" asp-controller="User" asp-action="Profile">
                @User.Identity!.Name
            </a>
            <small class="text-muted">@User.FindFirstValue(ClaimTypes.Role)</small>
        </div>
    </div>

    @* Sidebar Menu *@
    <nav class="mt-3">
        <ul class="nav nav-pills nav-sidebar flex-column" role="menu">
            @foreach (var item in menuItems)
            {
                <li class="nav-item">
                    <a href="@Url.Action("Index", item.Controller)"
                       class="nav-link text-light @(Model == item.Controller ? "active" : "")">
                        <i class="nav-icon @item.Icon me-2"></i>
                        <span>@item.Title</span>
                    </a>
                </li>
            }

            @* Settings Dropdown *@
            <li class="nav-item">
                <a href="#"
                   class="nav-link text-light @(settingsItems.Any(x => x.Controller == Model) ? "active" : "")"
                   data-bs-toggle="collapse"
                   data-bs-target="#settingsSubmenu"
                   aria-expanded="@(settingsItems.Any(x => x.Controller == Model) ? "true" : "false")">
                    <i class="nav-icon fas fa-cogs me-2"></i>
                    <span>
                        الإعدادات
                        <i class="fas fa-angle-down ms-2"></i>
                    </span>
                </a>
                <div class="collapse @(settingsItems.Any(x => x.Controller == Model) ? "show" : "")"
                     id="settingsSubmenu">
                    <ul class="nav nav-treeview ms-3">
                        @foreach (var item in settingsItems)

                        {
                            <li class="nav-item">
                                <a href="@Url.Action("Index", item.Controller)"
                                   class="nav-link text-light @(Model == item.Controller ? "active" : "")">
                                    <i class="@item.Icon me-2"></i>
                                    <span>@item.Title</span>
                                </a>
                            </li>
                        }
                    </ul>
                </div>
            </li>
        </ul>
    </nav>
</aside>
