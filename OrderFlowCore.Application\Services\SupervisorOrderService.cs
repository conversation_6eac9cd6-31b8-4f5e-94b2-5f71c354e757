using Microsoft.Extensions.Logging;
using OrderFlowCore.Application.Common;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Helper;
using OrderFlowCore.Application.Interfaces.Repositories;
using OrderFlowCore.Core.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace OrderFlowCore.Application.Services
{
    public interface ISupervisorOrderService
    {
        Task<ServiceResult<List<OrderSummaryDto>>> GetSupervisorOrdersAsync(string supervisorRole);
        Task<ServiceResult> ConfirmOrderBySupervisorAsync(int orderId, string supervisorRole, string userName);
        Task<ServiceResult> NeedsActionBySupervisorAsync(int orderId, string actionRequired, string supervisorRole, string userName);
        Task<ServiceResult> RejectOrderBySupervisorAsync(int orderId, string rejectReason, string supervisorRole, string userName);
    }

    public class SupervisorOrderService : ISupervisorOrderService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<SupervisorOrderService> _logger;

        public SupervisorOrderService(
            IUnitOfWork unitOfWork,
            ILogger<SupervisorOrderService> logger)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public async Task<ServiceResult<List<OrderSummaryDto>>> GetSupervisorOrdersAsync(string supervisorRole)
        {
            try
            {
                var orders = await _unitOfWork.Orders.GetSupervisorOrdersAsync(supervisorRole);
                var orderSummaries = orders.Select(order => new OrderSummaryDto
                {
                    Id = order.Id,
                    EmployeeName = order.EmployeeName,
                    OrderType = order.OrderType,
                    Department = order.Department,
                    CreatedAt = order.CreatedAt,
                    OrderStatus = order.OrderStatus
                }).ToList();
                return ServiceResult<List<OrderSummaryDto>>.Success(orderSummaries);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting supervisor orders for role {SupervisorRole}", supervisorRole);
                return ServiceResult<List<OrderSummaryDto>>.Failure($"حدث خطأ أثناء جلب الطلبات: {ex.Message}");
            }
        }

        public async Task<ServiceResult> ConfirmOrderBySupervisorAsync(int orderId, string supervisorRole, string userName)
        {
            try
            {
                if (orderId <= 0)
                {
                    return ServiceResult.Failure("رقم الطلب غير صحيح");
                }

                if (string.IsNullOrWhiteSpace(supervisorRole))
                {
                    return ServiceResult.Failure("دور المشرف مطلوب");
                }

                var order = await _unitOfWork.Orders.GetByIdAsync(orderId);
                if (order == null)
                {
                    return ServiceResult.Failure("لم يتم العثور على الطلب");
                }

                // Update supervisor status to confirmed
                var confirmedStatus = OrderHelper.ConfirmedBy(userName);
                await UpdateSupervisorStatus(order, supervisorRole, confirmedStatus);

                // Check if all required supervisors have confirmed
                var allConfirmed = await CheckAllSupervisorsConfirmed(order);
                if (allConfirmed)
                {
                    order.OrderStatus = OrderStatus.Accepted;
                }

                await _unitOfWork.Orders.UpdateAsync(order);
                await _unitOfWork.SaveChangesAsync();

                var message = allConfirmed ? "تم اعتماد الطلب بنجاح من جميع المشرفين" : "تم اعتماد الطلب من المشرف بنجاح";
                return ServiceResult.Success(message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error confirming order {OrderId} by supervisor {SupervisorRole}", orderId, supervisorRole);
                return ServiceResult.Failure($"حدث خطأ أثناء اعتماد الطلب: {ex.Message}");
            }
        }

        public async Task<ServiceResult> NeedsActionBySupervisorAsync(int orderId, string actionRequired, string supervisorRole, string userName)
        {
            try
            {
                if (orderId <= 0)
                {
                    return ServiceResult.Failure("رقم الطلب غير صحيح");
                }

                if (string.IsNullOrWhiteSpace(actionRequired))
                {
                    return ServiceResult.Failure("تفاصيل الإجراء المطلوب مطلوبة");
                }

                if (string.IsNullOrWhiteSpace(supervisorRole))
                {
                    return ServiceResult.Failure("دور المشرف مطلوب");
                }

                var order = await _unitOfWork.Orders.GetByIdAsync(orderId);
                if (order == null)
                {
                    return ServiceResult.Failure("لم يتم العثور على الطلب");
                }

                // Update supervisor status to needs action
                var actionStatus = $"{DateTime.Now:yyyy-MM-dd} | يتطلب إجراء: {actionRequired} - {userName}";
                await UpdateSupervisorStatus(order, supervisorRole, actionStatus);

                // Update order status to action required by supervisor
                order.OrderStatus = OrderStatus.ActionRequiredBySupervisor;

                await _unitOfWork.Orders.UpdateAsync(order);
                await _unitOfWork.SaveChangesAsync();

                return ServiceResult.Success("تم تسجيل طلب الإجراء بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error setting action required for order {OrderId} by supervisor {SupervisorRole}", orderId, supervisorRole);
                return ServiceResult.Failure($"حدث خطأ أثناء تسجيل طلب الإجراء: {ex.Message}");
            }
        }

        public async Task<ServiceResult> RejectOrderBySupervisorAsync(int orderId, string rejectReason, string supervisorRole, string userName)
        {
            try
            {
                if (orderId <= 0)
                {
                    return ServiceResult.Failure("رقم الطلب غير صحيح");
                }

                if (string.IsNullOrWhiteSpace(rejectReason))
                {
                    return ServiceResult.Failure("سبب الرفض مطلوب");
                }

                if (string.IsNullOrWhiteSpace(supervisorRole))
                {
                    return ServiceResult.Failure("دور المشرف مطلوب");
                }

                var order = await _unitOfWork.Orders.GetByIdAsync(orderId);
                if (order == null)
                {
                    return ServiceResult.Failure("لم يتم العثور على الطلب");
                }

                // Update supervisor status to rejected
                var rejectedStatus = OrderHelper.RejectedBy(userName);
                await UpdateSupervisorStatus(order, supervisorRole, rejectedStatus);

                // Update order status and reason
                order.OrderStatus = OrderStatus.CancelledBySupervisor;
                order.ReasonForCancellation = $"رفض من {supervisorRole}: {rejectReason}";

                await _unitOfWork.Orders.UpdateAsync(order);
                await _unitOfWork.SaveChangesAsync();

                return ServiceResult.Success("تم رفض الطلب بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error rejecting order {OrderId} by supervisor {SupervisorRole}", orderId, supervisorRole);
                return ServiceResult.Failure($"حدث خطأ أثناء رفض الطلب: {ex.Message}");
            }
        }

        #region Private Helper Methods

        private async Task UpdateSupervisorStatus(Core.Models.OrdersTable order, string supervisorRole, string status)
        {
            // This method updates the specific supervisor column based on the supervisor role
            // The actual implementation depends on how supervisor roles map to order table columns
            // For now, we'll use a simple mapping - this should be enhanced based on your specific requirements

            var supervisorMapping = new Dictionary<string, Action<Core.Models.OrdersTable, string>>
            {
                ["SupervisorOfEmployeeServices"] = (o, s) => o.SupervisorOfEmployeeServices = s,
                ["SupervisorOfHumanResourcesPlanning"] = (o, s) => o.SupervisorOfHumanResourcesPlanning = s,
                ["SupervisorOfInformationTechnology"] = (o, s) => o.SupervisorOfInformationTechnology = s,
                ["SupervisorOfAttendance"] = (o, s) => o.SupervisorOfAttendance = s,
                ["SupervisorOfMedicalRecords"] = (o, s) => o.SupervisorOfMedicalRecords = s,
                ["SupervisorOfPayrollAndBenefits"] = (o, s) => o.SupervisorOfPayrollAndBenefits = s,
                ["SupervisorOfLegalAndCompliance"] = (o, s) => o.SupervisorOfLegalAndCompliance = s,
                ["SupervisorOfHumanResourcesServices"] = (o, s) => o.SupervisorOfHumanResourcesServices = s,
                ["SupervisorOfHousing"] = (o, s) => o.SupervisorOfHousing = s,
                ["SupervisorOfFiles"] = (o, s) => o.SupervisorOfFiles = s,
                ["SupervisorOfOutpatientClinics"] = (o, s) => o.SupervisorOfOutpatientClinics = s,
                ["SupervisorOfSocialSecurity"] = (o, s) => o.SupervisorOfSocialSecurity = s,
                ["SupervisorOfInventoryControl"] = (o, s) => o.SupervisorOfInventoryControl = s,
                ["SupervisorOfRevenueDevelopment"] = (o, s) => o.SupervisorOfRevenueDevelopment = s,
                ["SupervisorOfSecurity"] = (o, s) => o.SupervisorOfSecurity = s,
                ["SupervisorOfMedicalConsultation"] = (o, s) => o.SupervisorOfMedicalConsultation = s
            };

            if (supervisorMapping.TryGetValue(supervisorRole, out var updateAction))
            {
                updateAction(order, status);
            }
            else
            {
                _logger.LogWarning("Unknown supervisor role: {SupervisorRole}", supervisorRole);
                throw new ArgumentException($"Unknown supervisor role: {supervisorRole}");
            }
        }

        private async Task<bool> CheckAllSupervisorsConfirmed(Core.Models.OrdersTable order)
        {
            // This method checks if all assigned supervisors have confirmed the order
            // Implementation depends on your business logic for determining which supervisors are required
            // For now, we'll implement a basic check - this should be enhanced based on your requirements

            var supervisorStatuses = new List<string>
            {
                order.SupervisorOfEmployeeServices,
                order.SupervisorOfHumanResourcesPlanning,
                order.SupervisorOfInformationTechnology,
                order.SupervisorOfAttendance,
                order.SupervisorOfMedicalRecords,
                order.SupervisorOfPayrollAndBenefits,
                order.SupervisorOfLegalAndCompliance,
                order.SupervisorOfHumanResourcesServices,
                order.SupervisorOfHousing,
                order.SupervisorOfFiles,
                order.SupervisorOfOutpatientClinics,
                order.SupervisorOfSocialSecurity,
                order.SupervisorOfInventoryControl,
                order.SupervisorOfRevenueDevelopment,
                order.SupervisorOfSecurity,
                order.SupervisorOfMedicalConsultation
            };

            // Get only the assigned supervisors (those with "under implementation" status)
            var underImplementationStatus = OrderHelper.OrderUnderImplementation();
            var assignedSupervisors = supervisorStatuses.Where(s => !string.IsNullOrEmpty(s) && s.Contains("قيد التنفيذ")).ToList();

            if (assignedSupervisors.Count == 0)
            {
                return false; // No supervisors assigned
            }

            // Check if all assigned supervisors have confirmed (status contains "اعتماد")
            var confirmedSupervisors = supervisorStatuses.Where(s => !string.IsNullOrEmpty(s) && s.Contains("اعتماد")).Count();

            return confirmedSupervisors >= assignedSupervisors.Count;
        }

        #endregion
    }
}
