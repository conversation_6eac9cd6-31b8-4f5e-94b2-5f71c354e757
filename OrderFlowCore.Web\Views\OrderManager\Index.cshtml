@model OrderFlowCore.Web.ViewModels.OrderManagerViewModel
@{
    ViewBag.Title = "إدارة الطلبات";
}

<div>
    <h2 class="mb-4">إدارة الطلبات</h2>
    @if (!string.IsNullOrEmpty(Model.SuccessMessage))
    {
        <div class="alert alert-success">@Model.SuccessMessage</div>
    }
    @if (!string.IsNullOrEmpty(Model.ErrorMessage))
    {
        <div class="alert alert-danger">@Model.ErrorMessage</div>
    }

    <!-- Order selection and actions -->
    <div class="mb-3">
        <form asp-action="Index" method="get" class="form-inline">
            <label class="me-2">رقم الطلب:</label>
            <select name="SelectedId" class="form-select form-select-lg d-inline w-auto" onchange="this.form.submit()">
                <option value="">-- اختر رقم الطلب --</option>
                @foreach (var order in Model.Orders)
                {
                    @* <option value="@order.Id" @(Model.SelectedId == order.Id ? "selected" : "")>@order.DisplayText</option> *@
                }
            </select>
        </form>
    </div>

    @if (Model.SelectedOrderDetails != null)
    {
        <div class="full-table-container">
            <table class="details-table">
                <tr>
                    <th>رقم الطلب</th>
                    <td>@Model.SelectedOrderDetails.Id</td>
                    <th>نوع الطلب</th>
                    <td>@Model.SelectedOrderDetails.OrderType</td>
                </tr>
                <tr>
                    <th>اسم الموظف</th>
                    <td>@Model.SelectedOrderDetails.EmployeeName</td>
                    <th>القسم</th>
                    <td>@Model.SelectedOrderDetails.Department</td>
                </tr>
                <tr>
                    <th>الوظيفة</th>
                    <td>@Model.SelectedOrderDetails.JobTitle</td>
                    <th>نوع التوظيف</th>
                    <td>@Model.SelectedOrderDetails.EmploymentType</td>
                </tr>
                <tr>
                    <th>المؤهل</th>
                    <td>@Model.SelectedOrderDetails.Qualification</td>
                    <th>رقم الموظف</th>
                    <td>@Model.SelectedOrderDetails.EmployeeNumber</td>
                </tr>
                <tr>
                    <th>السجل المدني</th>
                    <td>@Model.SelectedOrderDetails.CivilRecord</td>
                    <th>الجنسية</th>
                    <td>@Model.SelectedOrderDetails.Nationality</td>
                </tr>
                <tr>
                    <th>رقم الجوال</th>
                    <td>@Model.SelectedOrderDetails.MobileNumber</td>
                    <th>ملاحظات</th>
                    <td>@Model.SelectedOrderDetails.SupervisorNotes</td>
                </tr>
                <tr>
                    <th>مدير الموارد البشرية</th>
                    <td colspan="3">@Model.SelectedOrderDetails.ConfirmedByAssistantManager</td>
                </tr>
            </table>

            <table class="details-table">
                <tr>
                    <th>مشرف خدمات الموظفين</th>
                    <td>@Model.SelectedOrderDetails.SupervisorOfEmployeeServices</td>
                    <th>مشرف إدارة تخطيط الموارد البشرية</th>
                    <td>@Model.SelectedOrderDetails.SupervisorOfHumanResourcesPlanning</td>
                </tr>
                <tr>
                    <th>مشرف إدارة تقنية المعلومات</th>
                    <td>@Model.SelectedOrderDetails.SupervisorOfInformationTechnology</td>
                    <th>مشرف مراقبة الدوام</th>
                    <td>@Model.SelectedOrderDetails.SupervisorOfAttendance</td>
                </tr>
                <tr>
                    <th>مشرف السجلات الطبية</th>
                    <td>@Model.SelectedOrderDetails.SupervisorOfMedicalRecords</td>
                    <th>مشرف إدارة تقنية المعلومات</th>
                    <td>@Model.SelectedOrderDetails.SupervisorOfPayrollAndBenefits</td>
                </tr>
                <tr>
                    <th>مشرف إدارة القانونية والالتزام</th>
                    <td>@Model.SelectedOrderDetails.SupervisorOfLegalAndCompliance</td>
                    <th>مشرف خدمات الموارد البشرية</th>
                    <td>@Model.SelectedOrderDetails.SupervisorOfHumanResourcesServices</td>
                </tr>
                <tr>
                    <th>مشرف إدارة الإسكان</th>
                    <td>@Model.SelectedOrderDetails.SupervisorOfHousing</td>
                    <th>مشرف قسم الملفات</th>
                    <td>@Model.SelectedOrderDetails.SupervisorOfFiles</td>
                </tr>
                <tr>
                    <th>مشرف العيادات الخارجية</th>
                    <td>@Model.SelectedOrderDetails.SupervisorOfOutpatientClinics</td>
                    <th>مشرف التأمينات الاجتماعية</th>
                    <td>@Model.SelectedOrderDetails.SupervisorOfSocialSecurity</td>
                </tr>
                <tr>
                    <th>مشرف وحدة مراقبة المخزون</th>
                    <td>@Model.SelectedOrderDetails.SupervisorOfInventoryControl</td>
                    <th>مشرف إدارة تنمية الإيرادات</th>
                    <td>@Model.SelectedOrderDetails.SupervisorOfRevenueDevelopment</td>
                </tr>
                <tr>
                    <th>مشرف إدارة الأمن و السلامة</th>
                    <td>@Model.SelectedOrderDetails.SupervisorOfSecurity</td>
                    <th>مشرف الطب الاتصالي</th>
                    <td>@Model.SelectedOrderDetails.SupervisorOfMedicalConsultation</td>
                </tr>
            </table>

            <!-- Download Attachments -->
            <form asp-action="DownloadAttachments" method="get">
                <input type="hidden" name="orderId" value="@Model.SelectedOrderDetails.Id" />
                <button type="submit" class="submit-button">تحميل مرفقات الطلب</button>
            </form>
        </div>

        <!-- Confirm, Reject, Return -->
        <div class="mt-4">
            <form asp-action="Confirm" method="post" class="d-inline">
                <input type="hidden" name="orderId" value="@Model.SelectedOrderDetails.Id" />
                <button type="submit" class="submit-button">اعتماد</button>
            </form>
            <form asp-action="Reject" method="post" class="d-inline ms-2">
                <input type="hidden" name="orderId" value="@Model.SelectedOrderDetails.Id" />
                <input type="text" name="reason" placeholder="سبب الإلغاء" class="form-control d-inline w-auto me-2" required />
                <button type="submit" class="reject-button">إلغاء</button>
            </form>
            <form asp-action="Return" method="post" class="d-inline ms-2">
                <input type="hidden" name="orderId" value="@Model.SelectedOrderDetails.Id" />
                <input type="text" name="reason" placeholder="سبب الإعادة" class="form-control d-inline w-auto me-2" required />
                <button type="submit" class="return-button">إعادة</button>
            </form>
        </div>
    }

    <!-- Toggle Change Status Section -->
    <button class="toggle-button mt-4" type="button" data-bs-toggle="collapse" data-bs-target="#changeStatusSection">إظهار/إخفاء تغيير حالة الطلب</button>
    <div class="collapse mt-3" id="changeStatusSection">
        <form asp-action="ChangeStatus" method="post">
            <div class="mb-3">
                <label>اختر الطلب:</label>
                <select name="orderId" class="form-select">
                    <option value="">-- اختر الطلب --</option>
                    @foreach (var order in Model.Orders)
                    {
                        <option value="@order.OrderId">@order.DisplayText</option>
                    }
                </select>
            </div>
            <div class="mb-3">
                <label>الحالة الجديدة:</label>
                <select name="newStatus" class="form-select">
                    <option value="">-- اختر الحالة الجديدة --</option>
                    @foreach (var status in Model.StatusOptions)
                    {
                        <option value="@status.Value">@status.Text</option>
                    }
                </select>
            </div>
            <div class="mb-3">
                <label>ملاحظات التغيير (اختياري):</label>
                <textarea name="notes" class="form-control" rows="3"></textarea>
            </div>
            <button type="submit" class="submit-button">تأكيد التغيير</button>
        </form>
    </div>
</div> 