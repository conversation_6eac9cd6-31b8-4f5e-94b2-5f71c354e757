<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>نظام إدارة الطلبات - مستشفى بريدة المركزي - @ViewData["Title"]</title>
    
    <!-- Modern CSS Framework -->
    <link href="~/lib/bootstrap/dist/css/bootstrap.rtl.min.css" rel="stylesheet" />
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;900&display=swap" rel="stylesheet">

    <link rel="stylesheet" href="~/css/MainSite.css" asp-append-version="true" />
    
</head>
<body>
    <!-- Modern Navbar -->
    <nav class="navbar navbar-expand-lg navbar-modern fixed-top">
        <div class="container">
            <a class="navbar-brand d-flex align-items-center" asp-controller="Home" asp-action="Index">
                <img src="~/img/Breada1.png" alt="Logo" style="height: 50px;">
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <i class="fas fa-bars"></i>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item"> 
                        <a class="nav-link" asp-controller="Home" asp-action="Index">
                            <i class="fas fa-home me-2"></i> الرئيسية
                        </a>
                    </li> 
                    <li class="nav-item">
                        <a class="nav-link" asp-controller="Order" asp-action="New">
                            <i class="fas fa-plus-circle me-2"></i> طلب جديد
                        </a>
                    </li>
                   <li class="nav-item">
                            <a class="nav-link" asp-controller="Home" asp-action="Instructions">
                                <i class="fas fa-question-circle me-1"></i> التعليمات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="Home" asp-action="Contact">
                                <i class="fas fa-envelope me-1"></i> تواصل معنا
                            </a>
                        </li>
                      
                </ul> 
                <partial name="_LoginPartial" />
            </div>
        </div>
    </nav>
    <div class="container-fluid" style="margin-top: 75px;">
        <main role="main">
            @RenderBody()
        </main>
    </div>
    <!-- Modern Footer -->
    <footer class="footer-modern py-5">
        <div class="container">
            <div class="row g-4">
                <div class="col-lg-4">
                    <h5 class="fw-bold mb-3">نظام إدارة الطلبات</h5>
                    <p class="text-muted">منصة رقمية متكاملة تقدم حلولاً ذكية لإدارة جميع أنواع الطلبات بكفاءة عالية</p>
                    <div class="d-flex gap-3 mt-3">
                        <a href="#" class="text-white fs-5"><i class="fab fa-facebook"></i></a>
                        <a href="#" class="text-white fs-5"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="text-white fs-5"><i class="fab fa-linkedin"></i></a>
                        <a href="#" class="text-white fs-5"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>
                <div class="col-lg-2">
                    <h6 class="fw-bold mb-3">روابط سريعة</h6>
                    <ul class="list-unstyled">
                        <li class="mb-2"><a asp-controller="Order" asp-action="New" class="footer-link">طلب جديد</a></li>
                        <li class="mb-2"><a href="@Url.Action("Index", "Home")#search-section" class="footer-link">تتبع الطلبات</a></li>
                        <li class="mb-2"><a asp-controller="Home" asp-action="Contact" class="footer-link">الدعم الفني</a></li>
                    </ul>
                </div>
                <div class="col-lg-2">
                    <h6 class="fw-bold mb-3">المساعدة</h6>
                    <ul class="list-unstyled">
                        <li class="mb-2"><a asp-controller="Home" asp-action="Instructions" class="footer-link">التعليمات</a></li>
                        <li class="mb-2"><a asp-controller="Home" asp-action="Contact" class="footer-link">الأسئلة الشائعة</a></li>
                        <li class="mb-2"><a asp-controller="Home" asp-action="Privacy" class="footer-link">سياسة الخصوصية</a></li>
                       
                    </ul>
                </div>
                <div class="col-lg-4">
                    <h6 class="fw-bold mb-3">تواصل معنا</h6>
                    <ul class="list-unstyled">
                        <li class="mb-2">
                            <i class="fas fa-map-marker-alt me-2 text-primary"></i>
                            مستشفى بريدة المركزي، القصيم، المملكة العربية السعودية
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-phone me-2 text-primary"></i>
                            <span dir="ltr">+966 16 325 1111</span>
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-envelope me-2 text-primary"></i>
                            <EMAIL>
                        </li>
                    </ul>
                </div>
            </div>
            <hr class="my-4" style="border-color: rgba(255,255,255,0.1);">
            <div class="text-center">
                <p class="mb-0 text-muted">&copy; 2025 نظام إدارة الطلبات - مستشفى بريدة المركزي. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </footer>

    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>
    @await RenderSectionAsync("Scripts", required: false)
    
    <!-- Custom JavaScript -->
    <script>
        // Navbar scroll effect
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.navbar-modern');
            if (window.scrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Animation on scroll
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -100px 0px'
        };

        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.animation = 'fadeInUp 0.6s ease forwards';
                    observer.unobserve(entry.target);
                }
            });
        }, observerOptions);

        // Observe all feature cards and stat cards
        document.querySelectorAll('.feature-card, .stat-card, .process-step').forEach(el => {
            observer.observe(el);
        });

        // Counter animation for statistics
        function animateCounter(element, target) {
            let current = 0;
            const increment = target / 50;
            const timer = setInterval(() => {
                current += increment;
                if (current >= target) {
                    element.textContent = target + '+';
                    clearInterval(timer);
                } else {
                    element.textContent = Math.floor(current) + '+';
                }
            }, 30);
        }

        // Trigger counter animation when stats section is visible
        const statsObserver = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const statNumbers = entry.target.querySelectorAll('.stat-number');
                    statNumbers.forEach(stat => {
                        const text = stat.textContent;
                        if (text.includes('+')) {
                            const number = parseInt(text);
                            stat.textContent = '0';
                            animateCounter(stat, number);
                        }
                    });
                    statsObserver.unobserve(entry.target);
                }
            });
        }, { threshold: 0.5 });

        const statsSection = document.querySelector('.row.g-4');
        if (statsSection) {
            statsObserver.observe(statsSection);
        }

        // Add ripple effect to buttons
        document.querySelectorAll('.btn-modern').forEach(button => {
            button.addEventListener('click', function(e) {
                const ripple = document.createElement('span');
                const rect = this.getBoundingClientRect();
                const size = Math.max(rect.width, rect.height);
                const x = e.clientX - rect.left - size / 2;
                const y = e.clientY - rect.top - size / 2;
                
                ripple.style.width = ripple.style.height = size + 'px';
                ripple.style.left = x + 'px';
                ripple.style.top = y + 'px';
                ripple.classList.add('ripple');
                
                this.appendChild(ripple);
                
                setTimeout(() => ripple.remove(), 600);
            });
        });

        // Form validation
        const searchForm = document.querySelector('.search-card form');
        if (searchForm) {
            searchForm.addEventListener('submit', function(e) {
                e.preventDefault();
                const inputs = this.querySelectorAll('input');
                let isValid = true;
                
                inputs.forEach(input => {
                    if (!input.value.trim()) {
                        input.classList.add('is-invalid');
                        isValid = false;
                    } else {
                        input.classList.remove('is-invalid');
                    }
                });
                
                if (isValid) {
                    // Handle form submission
                    searchForm.submit();
                }
            });
        }

        // Add loading animation
        window.addEventListener('load', function() {
            document.body.classList.add('loaded');
        });
    </script>

    <!-- Toast Notification Container (bottom left) -->
    <div aria-live="polite" aria-atomic="true" style="position: fixed; bottom: 1rem; left: 1rem; z-index: 1080; min-width: 300px;">
        <div id="toastContainer" class="toast-container"></div>
    </div>
</body>
</html>